# 创新工坊预约系统API接口总结

## 系统架构

### 服务端口
- **FastAPI后端**: http://localhost:8001
- **Flask管理系统**: http://localhost:5000
- **API文档**: http://localhost:8001/docs

### 测试账号
- **用户名**: testuser
- **密码**: testpassword
- **权限**: admin

## API接口分类汇总

### 🔐 认证接口 (Authentication)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/token` | 用户登录获取JWT令牌 | ❌ |
| GET | `/api/users/me` | 获取当前用户信息 | ✅ |

### 📋 预约接口 (Reservations)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/reservations/my-reservations` | 获取我的预约记录 | ✅ |
| POST | `/api/reservations/venue` | 创建场地预约 | ✅ |
| POST | `/api/reservations/device` | 创建设备预约 | ✅ |
| POST | `/api/reservations/printer` | 创建打印机预约 | ✅ |
| GET | `/api/reservations/venue/occupied-times` | 查询场地占用时间 | ✅ |
| PUT | `/api/reservations/device-return/{id}` | 设备归还确认 | ✅ |
| PUT | `/api/reservations/printer-completion/{id}` | 打印完成确认 | ✅ |

### 🛠️ 管理接口 (Management)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/management/devices` | 获取设备列表 | ✅ |
| GET | `/api/management/venues` | 获取场地列表 | ✅ |
| GET | `/api/management/printers` | 获取打印机列表 | ✅ |
| POST | `/api/management/devices` | 添加设备/场地 | ✅ |
| PUT | `/api/management/devices/{id}` | 更新设备信息 | ✅ |
| DELETE | `/api/management/devices/{id}` | 删除设备 | ✅ |

### 👨‍💼 管理员接口 (Admin)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/admin/reservations/pending` | 获取待审批预约 | 🔑 |
| GET | `/api/admin/reservations/approved` | 获取已审批预约 | 🔑 |
| POST | `/api/admin/reservations/approve` | 审批预约 | 🔑 |
| POST | `/api/admin/reservations/reject` | 拒绝预约 | 🔑 |
| POST | `/api/admin/reservations/batch-approve` | 批量审批 | 🔑 |
| GET | `/api/admin/users` | 获取用户列表 | 🔑 |
| POST | `/api/admin/users/import` | 批量导入用户 | 🔑 |
| DELETE | `/api/admin/users/{username}` | 删除用户 | 🔑 |
| POST | `/api/admin/users/batch-delete` | 批量删除用户 | 🔑 |
| GET | `/api/admin/export-reservations` | 导出预约记录 | 🔑 |
| GET | `/api/admin/reservations/list` | 预约记录列表 | 🔑 |
| DELETE | `/api/admin/reservations/{type}/{id}` | 删除预约记录 | 🔑 |

### 🤖 AI接口 (Artificial Intelligence)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/ai/health` | AI服务健康检查 | ❌ |
| POST | `/api/ai/chat` | AI聊天对话 | ✅ |

### ⚙️ 设置接口 (Settings)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/settings/ai-feature` | 获取AI功能状态 | ❌ |
| POST | `/api/settings/ai-feature` | 更新AI功能状态 | ❌ |

### 📊 基础接口 (Basic)
| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/` | API根端点 | ❌ |
| GET | `/docs` | Swagger API文档 | ❌ |

## 图标说明
- ✅ 需要JWT认证
- ❌ 无需认证
- 🔑 需要管理员权限

## 核心业务流程

### 1. 用户预约流程
```
用户登录 → 查看可用资源 → 创建预约 → 等待审批 → 使用资源 → 归还/完成
```

### 2. 管理员审批流程
```
查看待审批列表 → 审批/拒绝预约 → 监控使用状态 → 确认归还/完成
```

### 3. AI助手流程
```
用户发起对话 → AI理解意图 → 提供建议/执行操作 → 返回结果
```

## 数据模型概览

### 用户模型 (User)
- user_id, username, name, department, role, password

### 场地预约模型 (VenueReservation)
- reservation_id, user_id, venue_type, reservation_date, business_time, purpose, devices_needed, status

### 设备预约模型 (DeviceReservation)
- reservation_id, user_id, device_name, borrow_time, return_time, reason, usage_type, status

### 打印机预约模型 (PrinterReservation)
- reservation_id, user_id, printer_name, reservation_date, print_time, end_time, model_name, status

### 管理模型 (Management)
- management_id, device_or_venue_name, category, quantity, available_quantity, status

## 状态枚举

### 预约状态
- `pending`: 待审批
- `approved`: 已通过
- `rejected`: 已拒绝
- `returned`: 已归还（设备）
- `completed`: 已完成（打印机）
- `return_pending`: 归还待审批
- `completion_pending`: 完成待审批

### 设备状态
- `available`: 可用
- `maintenance`: 维护中
- `unavailable`: 不可用

### 用户角色
- `student`: 学生
- `teacher`: 教师
- `admin`: 管理员

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权（需要登录） |
| 403 | 禁止访问（权限不足） |
| 404 | 资源不存在 |
| 422 | 请求参数验证失败 |
| 500 | 服务器内部错误 |

## 测试建议

### 1. 功能测试
- 使用Postman或curl测试各个接口
- 验证认证和权限控制
- 测试数据验证和错误处理

### 2. 性能测试
- 使用Apache Bench或Locust进行负载测试
- 测试并发预约场景
- 监控响应时间和资源使用

### 3. 集成测试
- 测试完整的业务流程
- 验证前后端数据一致性
- 测试异常情况处理

## 部署注意事项

### 1. 环境配置
- 配置数据库连接
- 设置JWT密钥
- 配置AI API密钥（如果使用）

### 2. 安全设置
- 启用HTTPS
- 配置CORS策略
- 设置适当的认证过期时间

### 3. 监控和日志
- 配置API访问日志
- 设置错误监控
- 监控系统性能指标

## 开发工具推荐

### 1. API测试
- **Postman**: 图形化API测试工具
- **curl**: 命令行HTTP客户端
- **HTTPie**: 用户友好的命令行HTTP客户端

### 2. 文档工具
- **Swagger UI**: 自动生成的API文档 (http://localhost:8001/docs)
- **ReDoc**: 另一种API文档展示方式

### 3. 开发调试
- **FastAPI**: 内置的交互式API文档
- **Python requests**: 用于编写测试脚本
- **Locust**: 性能测试工具

## 联系信息

如有问题或建议，请联系开发团队或查看项目文档。

---

**最后更新**: 2024年1月
**版本**: v1.0
**维护者**: 创新工坊开发团队
