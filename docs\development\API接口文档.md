# 🔌 API接口文档

## 概述

创新工坊预约系统提供RESTful API接口，支持前端应用和第三方系统集成。

## 基础信息

- **Base URL**: `http://localhost:8001/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

### 获取Token
```http
POST /auth/login
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "password"
}
```

### 使用Token
```http
Authorization: Bearer <your-jwt-token>
```

## 用户相关接口

### 用户注册
```http
POST /users/register
Content-Type: application/json

{
    "username": "newuser",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "password": "password"
}
```

### 获取用户信息
```http
GET /users/me
Authorization: Bearer <token>
```

### 更新用户信息
```http
PUT /users/me
Authorization: Bearer <token>
Content-Type: application/json

{
    "phone": "***********",
    "email": "<EMAIL>"
}
```

## 预约相关接口

### 场地预约

#### 创建场地预约
```http
POST /reservations/venues
Authorization: Bearer <token>
Content-Type: application/json

{
    "venue_type": "创新实验室",
    "reservation_date": "2025-06-15",
    "business_time": "上午",
    "purpose": "项目开发",
    "devices_needed": ["投影仪", "白板"]
}
```

#### 获取场地预约列表
```http
GET /reservations/venues?page=1&size=10
Authorization: Bearer <token>
```

#### 获取场地预约详情
```http
GET /reservations/venues/{reservation_id}
Authorization: Bearer <token>
```

### 设备预约

#### 创建设备预约
```http
POST /reservations/devices
Authorization: Bearer <token>
Content-Type: application/json

{
    "device_name": "Arduino开发板",
    "borrow_time": "2025-06-15T09:00:00",
    "return_time": "2025-06-15T17:00:00",
    "usage_type": "takeaway",
    "teacher_name": "张老师"
}
```

#### 设备归还
```http
PUT /reservations/devices/{reservation_id}/return
Authorization: Bearer <token>
Content-Type: application/json

{
    "device_condition": "normal",
    "return_note": "设备正常使用"
}
```

### 3D打印预约

#### 创建3D打印预约
```http
POST /reservations/printers
Authorization: Bearer <token>
Content-Type: application/json

{
    "start_time": "2025-06-15T10:00:00",
    "estimated_duration": 120,
    "model_name": "手机壳模型",
    "teacher_name": "李老师"
}
```

#### 完成3D打印
```http
PUT /reservations/printers/{reservation_id}/complete
Authorization: Bearer <token>
Content-Type: application/json

{
    "printer_condition": "normal",
    "completion_note": "打印成功"
}
```

## 管理接口

### 预约审核
```http
PUT /admin/reservations/{reservation_id}/approve
Authorization: Bearer <admin-token>
Content-Type: application/json

{
    "action": "approve",  // approve 或 reject
    "note": "审核通过"
}
```

### 用户管理
```http
GET /admin/users?page=1&size=10
Authorization: Bearer <admin-token>
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {
        // 响应数据
    },
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "参数验证失败",
        "details": {
            "field": "email",
            "message": "邮箱格式不正确"
        }
    }
}
```

## 状态码说明

- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证失败
- `500` - 服务器内部错误

## 分页参数

大部分列表接口支持分页：
- `page`: 页码，从1开始
- `size`: 每页数量，默认10，最大100

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| VALIDATION_ERROR | 参数验证失败 |
| AUTHENTICATION_FAILED | 认证失败 |
| PERMISSION_DENIED | 权限不足 |
| RESOURCE_NOT_FOUND | 资源不存在 |
| DUPLICATE_RESOURCE | 资源重复 |
| BUSINESS_ERROR | 业务逻辑错误 |

## 接口限流

- 普通用户：100次/分钟
- 管理员：500次/分钟
- 超出限制返回429状态码

## 开发工具

推荐使用以下工具进行API测试：
- Postman
- Insomnia
- curl
- HTTPie

---

*更多接口详情请参考源码中的路由定义*
