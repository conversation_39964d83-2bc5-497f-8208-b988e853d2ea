<div align="center">
  <img src="docs/images/readme/1746457657772.jpg" alt="创新工坊预约系统" >
</div>

# 🎉创新工坊预约系统🎉

一个全功能的场地、设备和打印机预约管理系统，专为高校创新工坊设计，支持多角色用户管理、预约审批流程、资源调度和实时状态跟踪。系统集成了AI智能助手、预约信息统计看板和容器化部署等先进功能。

## 📋 目录

- [✨ 主要特色](#-主要特色)
- [🏗️ 系统架构](#️-系统架构)
- [📖 项目背景与目标](#-项目背景与目标)
- [📁 项目结构说明](#-项目结构说明)
- [⚡ 核心功能概述](#-核心功能概述)
- [🛠️ 技术栈与依赖](#️-技术栈与依赖)
- [🚀 安装部署说明](#-安装部署说明)
- [📚 使用说明](#-使用说明)
- [📖 文档中心](#-文档中心)
- [🔧 常见问题与维护建议](#-常见问题与维护建议)
- [🤝 开发者与贡献说明](#-开发者与贡献说明)
- [📋 更新日志](#-更新日志)
- [📄 许可证与版权](#-许可证与版权)

## ✨ 主要特色

- 🤖 **AI智能助手**：集成DeepSeek AI模型，提供智能预约建议和自然语言交互
- 📊 **数据可视化**：实时统计图表、监控面板
- 🐳 **容器化部署**：支持Docker一键部署，简化运维管理
- 📱 **微信小程序**：原生小程序体验，支持多端同步
- 🔐 **安全管理**：完善的权限控制和数据安全保护

## 🏗️ 系统架构

### 系统架构图

<div align="center">
  <img src="docs/images/readme/1746458090927.png" alt="创新工坊预约系统" >
</div>

### 数据流转图

![1746458198218](docs/images/readme/1746458198218.png)

### 审批流程图

![1746458292911](docs/images/readme/1746458292911.png)

## 📖 项目背景与目标

创新工坊预约系统是一个面向高校师生的综合资源管理平台，旨在解决创新工坊场地、设备和打印机等资源的预约、管理和使用问题。系统支持多角色用户（学生、教师、管理员）分级管理，提供直观的用户界面和完善的后台管理功能，实现资源的高效利用与透明管理。

主要目标用户包括：(需求在docs/command.md里面)

- 学生：预约使用创新工坊的场地、设备和3D打印机
- 教师：指导学生并审批预约请求
- 管理员：管理系统资源、用户和审批流程

## 📁 项目结构说明

项目采用前后端分离架构，包含以下主要模块：

### 核心模块

- **app/**：FastAPI后端核心模块，提供RESTful API接口

  - `models/`：数据模型定义（用户、预约、设备等）
  - `routers/`：API路由和业务逻辑实现
  - `schemas/`：API请求和响应的数据模型
  - `utils/`：工具函数集合（认证、数据处理等）
  - `db/`：数据库初始化和迁移脚本
  - `database.py`：数据库连接配置
  - `main.py`：应用入口和配置
- **admin_system/**：基于Flask的后台管理系统

  - `app/`：Flask应用核心
    - `templates/`：管理界面HTML模板
    - `static/`：CSS、JavaScript等静态资源
    - `routes/`：管理界面路由和视图函数
    - `models.py`：管理系统数据模型
    - `services/`：业务逻辑服务层
  - `run.py`：管理系统启动脚本
  - `create_admin.py`：管理员账号创建工具
- **fore/**：微信小程序前端（用于用户端操作）

  - `pages/`：小程序页面组件
    - `index/`：首页
    - `venue/`：场地预约相关页面
    - `device/`：设备预约相关页面
    - `printer/`：打印机预约相关页面
    - `my/`：个人中心
    - `approval/`：审批相关页面
    - `records/`：预约记录查询
  - `miniapp/`：小程序核心组件和工具
  - `utils/`：前端工具函数
  - `images/`：图像资源
  - `app.js`：小程序入口配置

### 辅助文件

- `runme.py`：项目一键启动脚本，同时启动FastAPI和Flask服务
- `requirements.txt`：项目依赖列表
- `user_template.xlsx`：用户批量导入模板
- `app.db`：SQLite数据库文件

## ⚡ 核心功能概述

### 🤖 AI智能助手

- **智能对话**：基于DeepSeek AI模型的自然语言交互
- **预约助手**：AI可以理解用户需求并协助完成预约操作
- **资源推荐**：根据用户需求智能推荐合适的场地和设备
- **时间识别**：支持自然语言时间表述（如"明天下午"）
- **功能管控**：管理员可控制AI功能的开启和关闭

### 👥 用户管理

- 多角色用户系统：支持学生、教师和管理员角色
- 账号管理：支持单个和批量创建用户账号（Excel导入）
- 认证授权：基于JWT的用户认证和权限控制
- 分页显示：大数据量下的高效用户列表管理

### 🏢 场地预约

- 多种场地类型：讲座厅、研讨室、会议室等
- 分时段预约：上午、下午、晚上三个时段
- 设备需求：支持同时预约场地附属设备（大屏、笔记本、麦克风等）
- 预约审批：预约申请提交后需经管理员审批
- 时区处理：正确处理时区问题，确保时间准确性

### 🔧 设备预约

- 设备借用：支持多种设备类型（电动螺丝刀、万用表等）
- 使用方式：支持现场使用和外借两种模式
- 借还流程：完整的借用申请、审批、归还、验收流程
- 设备状态跟踪：实时记录设备状态变化
- AI识别：AI助手可自动识别设备使用类型

### 🖨️ 打印机预约

- 3D打印机管理：支持多台打印机预约
- 时间段预约：精确到小时的预约时间管理
- 打印完成确认：使用完成后的状态确认和审批

### 📊 后台管理与统计

- **资源管理**：场地、设备、打印机的增删改查
- **预约审批**：集中处理各类预约申请，支持分页显示
- **数据统计**：实时统计图表，包括预约趋势、类型分布等
- **用户管理**：用户账号的创建和权限调整
- **数据导出**：支持Excel格式的数据导出功能
- **进度监控**：用户导入进度条和任务状态跟踪

## 🛠️ 技术栈与依赖

### 后端技术

- **FastAPI**：主要API服务框架，提供高性能RESTful API
- **Flask**：管理系统Web框架，提供后台管理界面
- **SQLAlchemy**：ORM数据库映射工具，支持多种数据库
- **Pydantic**：数据验证和序列化，确保API数据安全
- **JWT**：用户认证和授权，保障系统安全
- **Uvicorn**：ASGI服务器，支持异步处理
- **httpx**：异步HTTP客户端，用于AI API调用

### AI与智能化

- **DeepSeek API**：AI对话模型，提供智能助手功能
- **自然语言处理**：时间识别、意图理解、实体提取
- **智能预约**：AI辅助的预约信息收集和提交

### 前端技术

- **微信小程序**：原生小程序开发，提供流畅用户体验
- **自定义TabBar**：灵活的导航栏控制，支持功能开关
- **Bootstrap 4**：管理系统响应式UI框架
- **Chart.js**：数据可视化图表库
- **jQuery**：管理系统JavaScript库

### 数据库与存储

- **SQLite**：开发环境轻量级数据存储
- **数据持久化**：支持Docker卷挂载
- **数据迁移**：支持迁移到MySQL、PostgreSQL等生产数据库
- **备份恢复**：完整的数据备份和恢复机制

### 部署与运维

- **Docker**：容器化部署，简化环境配置
- **Docker Compose**：多服务编排，一键启动
- **时区配置**：UTC+8时区支持，确保时间准确
- **负载均衡**：支持多实例部署和负载均衡配置

## 🚀 安装部署说明

### 🐳 Docker部署（推荐）

Docker部署是最简单快捷的部署方式，支持一键启动完整的系统环境。

#### 📚 Docker文档导航

- **🚀 [快速启动指南](DOCKER_QUICK_START.md)** - 5分钟快速部署
- **📖 [完整Docker文档](README.docker.md)** - 详细部署和运维指南

#### ⚡ 超快速部署

```bash
# 1. 获取代码
git clone https://github.com/zzyss-marker/newinno.git
cd newinno

# 2. 一键启动（包含MySQL数据库）
docker-compose up -d --build

# 3. 访问系统
# 🖥️ 管理系统：http://localhost:5000 (admin/admin123)
# 🔌 API服务：http://localhost:8001
# 📱 小程序：使用微信开发者工具打开 fore/ 目录
```

#### 🎯 Docker部署优势

- ✅ **一键部署**：包含MySQL数据库，无需额外配置
- ✅ **环境一致**：避免依赖冲突和环境差异
- ✅ **自动配置**：时区、数据库、管理员账号自动设置
- ✅ **数据持久化**：容器重启数据不丢失
- ✅ **易于维护**：支持备份、恢复、监控
- ✅ **生产就绪**：支持负载均衡和集群部署

#### 🛠️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐
│   用户/前端      │    │   管理员        │
└─────────┬───────┘    └─────────┬───────┘
          │ :8001               │ :5000
    ┌─────▼──────────────────────▼─────┐
    │     Docker容器环境              │
    │  ┌─────────────┐ ┌─────────────┐ │
    │  │  FastAPI    │ │   Flask     │ │
    │  │   后端      │ │  管理系统    │ │
    │  └─────────────┘ └─────────────┘ │
    └─────────────┬───────────────────┘
                  │ 内部网络
    ┌─────────────▼───────────────────┐
    │      MySQL 8.0 数据库          │
    └─────────────────────────────────┘
```

#### 📊 快速命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 备份数据
./backup.sh  # Linux/macOS

# 进入容器调试
docker-compose exec reservation-system bash
```

### 🔧 传统部署

#### 环境要求

- Python 3.8+
- Node.js 12+（用于微信小程序开发）
- 微信开发者工具（用于前端开发和调试）
- Docker（可选，用于容器化部署）

#### 安装步骤

1. **克隆代码库**

```bash
git clone [仓库地址]
cd [项目目录]
```

2. **安装后端依赖**

```bash
python3 -m venv myvenv
source myvenv/bin/activate  # Linux/Mac
.\venv\Scripts\activate   # Windows
pip install -r requirements.txt
```

3. **配置环境变量**

```bash
# 创建.env文件
cp .env.example .env
# 编辑.env文件，配置以下参数：
# DEEPSEEK_API_KEY=your_deepseek_api_key
# DEEPSEEK_BASE_URL=https://api.deepseek.com
# DEEPSEEK_MODEL=deepseek-chat
```

4. **初始化数据库**

```bash
# 首次运行会自动初始化数据库
python runme.py
```

5. **创建管理员账号**

```bash
cd admin_system
python create_admin.py --username admin --password your_password
```

6. **启动项目**

```bash
python runme.py
# 后台运行（Linux/Mac） #不输出到日志且在后台持续运行
nohup python3 runme.py > /dev/null 2>&1 &
```

7. **编译前端**

```bash
# 使用微信开发者工具打开fore目录
# 编译并预览小程序
```

### 📡 服务配置

- **后端API服务**：`http://localhost:8001`
- **管理系统**：`http://localhost:5000`
- **数据库文件**：项目根目录的 `app.db`
- **AI功能**：需要配置DeepSeek API密钥

## 📚 使用说明

### 📱 用户端（微信小程序）

#### 基础功能

1. **登录系统**：使用分配的账号和密码登录
2. **场地预约**：选择场地类型、日期和时段进行预约
3. **设备预约**：选择设备、借用时间和归还时间
4. **打印机预约**：选择打印机和使用时间段
5. **我的预约**：查看个人预约记录和状态

#### 🤖 AI助手功能

1. **智能对话**：点击AI助手图标，与小喵进行自然语言对话
2. **预约咨询**：询问场地、设备使用相关问题
3. **智能预约**：告诉AI你的需求，它可以帮你收集信息并提交预约
4. **时间识别**：支持"明天下午"、"下周三"等自然语言时间表述

#### 使用技巧

- AI助手可以理解复杂的预约需求
- 支持一次性预约多个资源
- 可以通过AI查询设备使用说明
- AI会根据你的需求推荐合适的场地和设备

### 🖥️ 管理系统

#### 基础管理

1. **登录系统**：访问 `http://localhost:5000` 使用管理员账号登录
2. **用户管理**：创建和管理用户账号，支持Excel批量导入
3. **资源管理**：管理场地、设备和打印机的基本信息
4. **预约管理**：处理预约申请和查看预约记录，支持分页显示

#### 📊 数据统计与监控

1. **统计面板**：查看用户数量、预约统计等关键指标
2. **图表分析**：预约趋势图、类型分布图、状态统计图
3. **数据导出**：支持Excel格式导出各类数据
4. **进度监控**：实时查看用户导入等任务进度

#### ⚙️ 系统设置

1. **AI功能管控**：开启/关闭小程序端AI助手功能
2. **权限管理**：调整用户角色和权限设置
3. **系统配置**：修改系统参数和业务规则

### 🔑 演示账号

- **管理员**：用户名 `admin`，密码 `admin123`
- **学生用户**：用户名 `student`，密码 `123456`
- **教师用户**：用户名 `teacher`，密码 `123456`

> 💡 **提示**：首次部署时，系统会自动创建管理员账号。其他用户可通过管理系统批量导入或单个创建。

## 📖 文档中心

为了更好地使用和维护系统，我们提供了完整的文档体系：

### 📚 [完整文档导航](docs/README.md)

### 🎯 快速导航

#### 👥 用户文档
- **[快速开始指南](docs/user/快速开始指南.md)** - 新用户5分钟上手
- **[用户操作手册](docs/user/用户操作手册.md)** - 详细功能说明

#### 💻 开发文档
- **[技术架构文档](docs/development/技术架构.md)** - 系统架构设计
- **[数据库设计文档](docs/development/数据库设计.md)** - 数据库结构说明
- **[API接口文档](docs/development/API接口文档.md)** - 接口规范和使用

#### 🚀 部署文档
- **[Docker部署指南](docs/development/deployment/Docker部署指南.md)** - 容器化部署
- **[MySQL迁移指南](docs/development/deployment/MySQL迁移指南.md)** - 数据库迁移
- **[环境配置指南](docs/development/deployment/环境配置指南.md)** - 环境搭建

#### 🔒 安全文档
- **[安全总览](docs/security/安全总览.md)** - 系统安全架构
- **[渗透测试报告](docs/security/渗透测试报告.md)** - 安全测试结果
- **[安全部署检查清单](docs/security/安全部署检查清单.md)** - 部署安全检查

#### 📊 报告文档
- **[技术创新点文档](docs/reports/技术创新点文档.md)** - 技术亮点说明
- **[数据库选型分析](docs/reports/数据库选型分析报告.md)** - SQLite vs MySQL
- **[技术扩展规划](docs/reports/技术扩展规划.md)** - 未来发展规划

### 💡 文档使用建议

- **新用户**：先看[快速开始指南](docs/user/快速开始指南.md)
- **开发者**：重点关注[技术架构](docs/development/技术架构.md)和[API文档](docs/development/API接口文档.md)
- **运维人员**：参考[部署指南](docs/development/deployment/)和[安全文档](docs/security/)
- **项目管理**：查看[技术创新点](docs/reports/技术创新点文档.md)和[扩展规划](docs/reports/技术扩展规划.md)

## 🔧 常见问题与维护建议

### ❓ 常见问题

#### 部署相关

1. **Docker启动失败**

   ```bash
   # 查看容器状态
   docker-compose ps
   # 查看详细日志
   docker-compose logs -f
   ```
2. **传统部署启动失败**

   - 检查Python依赖是否完整安装
   - 检查端口是否被占用（8001和5000）
   - 查看日志输出定位具体错误
3. **时区问题**

   - Docker部署已自动配置UTC+8时区
   - 传统部署需要确保系统时区正确

#### 功能相关

4. **AI功能不可用**

   - 检查 `.env`文件中的DeepSeek API密钥配置
   - 确认网络连接正常，可访问DeepSeek API
   - 在管理系统中检查AI功能开关状态
5. **数据库错误**

   - 检查数据库文件权限
   - 如数据库损坏，可删除 `app.db` 并重新启动系统初始化
   - Docker部署时检查卷挂载是否正确
6. **前端连接失败**

   - 检查API地址配置（`fore/config.js`）
   - 确保后端服务正常运行
   - 检查网络防火墙设置

### 🛠️ 维护建议

#### 数据安全

1. **数据备份**

   ```bash
   # Docker环境备份
   docker run --rm -v reservation-system_app-data:/app -v $(pwd)/backup:/backup alpine sh -c "cp /app/app.db /backup/app_$(date +%Y%m%d).db"

   # 传统环境备份
   cp app.db backup/app_$(date +%Y%m%d).db
   cp admin_system/instance/admin.db backup/admin_$(date +%Y%m%d).db
   ```
2. **定期备份策略**

   - 建议每日自动备份数据库
   - 考虑使用生产环境数据库（MySQL/PostgreSQL）
   - 重要数据异地备份

#### 系统升级

3. **升级流程**

   - 先在测试环境验证新功能
   - 升级前完整备份数据库和配置文件
   - 使用Docker部署可简化升级过程
4. **版本管理**

   - 使用Git标签管理版本
   - 记录每次升级的变更内容
   - 保留回滚方案

#### 性能优化

5. **监控指标**

   - API响应时间和数据库查询性能
   - 系统资源使用情况（CPU、内存、磁盘）
   - 用户并发数和预约成功率
6. **性能测试**

   ```bash
   # 压力测试
   cd test
   locust -f locustfile.py --host=http://localhost:8001

   # 生成测试账号
   python create_test_user.py

   # 并发预约测试
   python bulk_reservation_test.py --count 10000 --threads 20
   ```
7. **优化建议**

   - 适当调整并发连接数和缓存策略
   - 对大数据量查询实施分页
   - 考虑使用Redis缓存热点数据
   - 数据库索引优化

## 🤝 开发者与贡献说明

### 贡献方式

本项目支持社区贡献，欢迎通过以下方式参与：

- 🐛 **提交Issue**：报告Bug或建议新功能
- 🔧 **提交Pull Request**：改进代码或添加新功能
- 📚 **完善文档**：改进使用说明和开发文档
- 🧪 **测试反馈**：提供测试结果和性能反馈

### 开发规范

- **代码风格**：遵循PEP 8规范（Python）和微信小程序开发规范
- **提交信息**：使用明确的描述格式
  - `feat: 添加新功能`
  - `fix: 修复bug`
  - `docs: 更新文档`
  - `style: 代码格式调整`
  - `refactor: 代码重构`
  - `test: 添加测试`
- **测试要求**：新功能需包含必要的测试用例
- **文档更新**：重要功能变更需同步更新README和相关文档

### 技术栈要求

- **后端开发**：熟悉FastAPI、Flask、SQLAlchemy
- **前端开发**：熟悉微信小程序开发、JavaScript
- **AI功能**：了解大语言模型API调用和自然语言处理
- **运维部署**：熟悉Docker、Linux系统管理

### 项目结构

详细的项目结构说明请参考上文的"项目结构说明"部分。开发前请仔细阅读代码结构，遵循现有的架构模式。

## 📋 更新日志

### v2.0.0 (最新版本)

#### 🆕 新增功能

- **AI智能助手**：集成DeepSeek AI模型，支持自然语言预约
- **容器化部署**：完整的Docker支持，一键部署
- **数据可视化**：管理系统统计图表和实时监控
- **分页显示**：大数据量下的高效列表管理
- **时区支持**：正确处理UTC+8时区问题
- **功能开关**：AI功能的动态开启/关闭控制

#### 🔧 改进优化

- 优化了预约流程的用户体验
- 改进了数据导入的进度显示
- 增强了系统的并发处理能力
- 完善了错误处理和日志记录

#### 🐛 问题修复

- 修复了时间显示不准确的问题
- 解决了大量数据加载时的性能问题
- 修复了AI功能在某些情况下的异常

### v1.0.0 (基础版本)

#### 🎉 初始功能

- 基础的场地、设备、打印机预约功能
- 用户管理和权限控制
- 预约审批流程
- 微信小程序前端
- Flask管理后台

## 📄 许可证与版权

本项目采用 **CC BY-NC-SA 4.0**（知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议），详情请查看 [LICENSE](LICENSE) 文件。

### 使用条款

#### ✅ 您可以：

- **共享** - 在任何媒介以任何形式复制、发行本作品
- **演绎** - 修改、转换或以本作品为基础进行创作
- **学术研究** - 用于教育和非营利性研究目的
- **个人学习** - 个人学习和技能提升

#### 📋 您必须：

- **署名** - 给出适当的署名和许可协议链接
- **标明修改** - 如果对原作品进行了修改，需要标明
- **相同许可** - 衍生作品必须使用相同的许可协议

#### ❌ 您不能：

- **商业使用** - 不得将本作品用于商业目的
- **附加限制** - 不得增加额外的法律或技术限制

> 💡 **说明**：教育机构的非营利性教学和研究活动不被视为商业使用。如需商业授权，请联系项目维护者。

### 致谢

- 感谢所有贡献者的努力和支持
- 感谢开源社区提供的优秀工具和框架
- 特别感谢DeepSeek提供的AI模型服务

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 **邮箱**：[项目邮箱](<EMAIL>)
- 🐛 **Issue**：[GitHub Issues](https://github.com/zzyss-marker/newinno/issues)

---

<div align="center">
  <p>⭐ 如果这个项目对你有帮助，请给我们一个星标！</p>
  <p>🚀 让我们一起构建更好的创新工坊管理系统！</p>
</div>
