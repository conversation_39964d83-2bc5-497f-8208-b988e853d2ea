# 创新工坊预约系统 - 项目需求文档

## 📋 项目概述

需要设计一个预约系统小程序，支持场地预约、设备预约、3D打印机预约等功能，面向高校创新工坊的师生用户。

## 🎯 核心需求

### 用户角色定义

1. **学生/教师（预约端）**：进行各类资源预约
2. **管理员（审批端）**：审批预约申请，管理系统资源
3. **系统管理员（管理端）**：用户管理、数据导入导出、系统配置

## 📱 前端架构要求

### 微信小程序架构

**首页设计**：
- 三大分栏：场地预约、设备预约、3D打印机预约
- **场地预约**下设三个按钮：讲座、研讨室、会议室
- **设备预约**下设：电动螺丝刀、万用表
- **3D打印机**下设：入口按钮（管理3台打印机）

**个人中心**：
- 用户登录功能
- 预约记录查看
- 设备归还管理

## 🏢 场地预约功能

### 预约界面要求

**基本信息**：
- 场地类型选择（讲座/研讨室/会议室）
- 日期选择器
- 时间段选择：上午/下午/晚上（复选框）
- 用途说明（文本输入）

**设备配套选择**：
- 大屏（复选框）
- 笔记本（复选框，选中时自动绑定投屏器）
- 话筒选择：手持麦/鹅颈麦（复选框）
- 投屏器（可单独选择）

**预约规则**：
- 场地需要提前3天预约
- 讲座需要较长时间，时间段选择更灵活

## 🔧 设备预约功能

### 预约界面要求

**基本信息**：
- 设备名称显示
- 借用时间选择（用户可选）
- 预计归还时间选择（用户可选）
- 借用原因（文本框输入）

**预约规则**：
- 设备可以随时预约
- 需要主动归还确认

**设备类型**：
- 电动螺丝刀
- 万用表

## 🖨️ 3D打印机预约功能

### 预约界面要求

**基本信息**：
- 打印机名称（打印机1、打印机2、打印机3）
- 打印时间选择（用户可选）
- 打印日期选择

**预约规则**：
- 需要提前1天申请
- 申请周期为一天
- 用户选择具体日期即可

## 👨‍💼 审批端功能

### 管理员界面要求

**权限控制**：
- 需要管理员账号登录才能访问

**审批功能**：
- 查看所有预约申请记录
- 点击查看申请详情：
  - 预约类型
  - 预约时间
  - 申请人信息
  - 审批状态
- 审批操作：同意/拒绝

**记录管理**：
- 区分已审批和未审批申请
- 设备归还确认审核
- 查看所有借用记录和归还记录

## 🖥️ 管理端功能

### Web管理界面

**用户管理**：
- Excel批量导入用户数据
- 数据模板提供：学号/工号、姓名、学院、身份证号后6位（作为密码）
- 区分学生和教师身份

**数据导出**：
- 场地预约记录导出
- 设备借用记录导出
- 3D打印机使用记录导出
- 导出内容包括：设备名/场地名、借用时间、归还时间、借用人、使用原因等

**资源管理**：
- 添加/修改场地信息
- 添加/修改设备信息
- 设备数量管理
- 设备使用状态显示

## 📊 数据记录要求

### 场地预约记录

- 场地名称
- 预约时间段
- 配套设备使用情况
- 预约人信息
- 使用目的
- 审批状态

### 设备借用记录

- 设备名称
- 借用时间
- 归还时间
- 借用人信息
- 借用原因
- 归还状态

### 3D打印机记录

- 打印机编号
- 使用日期
- 使用时长
- 使用人信息
- 打印项目说明

## 🔐 权限管理

### 角色权限

**学生/教师**：
- 提交预约申请
- 查看个人预约记录
- 确认设备归还

**管理员**：
- 审批所有预约申请
- 查看所有预约记录
- 确认设备归还

**系统管理员**：
- 用户数据管理
- 系统配置管理
- 数据导入导出
- 资源信息管理

## 📋 技术要求

### 前端要求

- 微信小程序原生开发
- 响应式设计，适配不同屏幕
- 良好的用户交互体验
- 数据实时同步

### 后端要求

- RESTful API设计
- 数据库设计合理
- 支持并发访问
- 数据安全保护

### 部署要求

- 支持云服务器部署
- 数据备份机制
- 系统监控功能
- 日志记录完整

## 🎯 项目目标

1. **提高资源利用效率**：通过预约系统合理分配创新工坊资源
2. **简化管理流程**：自动化审批和记录管理
3. **提升用户体验**：便捷的预约和查询功能
4. **数据透明化**：完整的使用记录和统计分析

## 📈 扩展功能

### 未来可扩展功能

- 预约冲突自动检测
- 使用统计分析报表
- 消息通知功能
- 预约评价系统
- 资源使用热力图

---

**文档版本**：v1.0  
**创建时间**：2025年6月15日  
**维护团队**：系统开发团队
