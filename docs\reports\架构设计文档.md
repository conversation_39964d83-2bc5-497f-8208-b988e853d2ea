# 创新工坊预约系统 - 架构设计文档

## 1. 项目概述

### 1.1 项目背景

创新工坊预约系统是一个面向高校创新实验室的综合性预约管理平台，支持场地、设备、打印机等资源的在线预约和管理。

### 1.2 设计目标

- **高可用性**：支持多用户并发访问，确保系统稳定运行
- **易用性**：提供直观的用户界面和流畅的操作体验
- **可扩展性**：模块化设计，便于功能扩展和维护
- **安全性**：完善的用户认证和权限管理机制

## 2. 技术架构

### 2.1 整体架构

![1748799113067](../images/architecture/1748799113067.png)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   管理系统Web    │    │   移动端H5      │
│   (前端界面)     │    │   (管理后台)     │    │   (备用方案)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (统一入口)     │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI后端    │    │   Flask管理系统  │    │   AI服务模块     │
│   (核心业务)     │    │   (管理功能)     │    │   (智能助手)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库    │
                    │   (数据存储)     │
                    └─────────────────┘
```

### 2.2 技术栈选型

#### 前端技术

- **微信小程序**：原生小程序开发，提供最佳用户体验
- **管理后台**：HTML5 + CSS3 + JavaScript + Bootstrap
- **UI框架**：WeUI（小程序）+ Bootstrap（Web）

#### 后端技术

- **FastAPI**：现代化Python Web框架，高性能异步API
- **Flask**：轻量级Web框架，用于管理系统
- **SQLAlchemy**：Python ORM框架，数据库操作
- **Pydantic**：数据验证和序列化

#### 数据库

- **MySQL 8.0**：主数据库，存储业务数据
- **Redis**：缓存和会话存储（可选扩展）

#### 部署技术

- **Docker**：容器化部署
- **Docker Compose**：多服务编排
- **Nginx**：反向代理和负载均衡（可选）

## 3. 系统架构

### 3.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Presentation Layer)               │
├─────────────────────────────────────────────────────────────┤
│  微信小程序界面  │  管理系统Web界面  │  AI对话界面           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  用户认证服务   │  预约管理服务   │  审批流程服务           │
│  权限控制服务   │  资源管理服务   │  AI智能服务             │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  预约业务逻辑   │  用户管理逻辑   │  资源调度逻辑           │
│  审批业务逻辑   │  统计分析逻辑   │  AI对话逻辑             │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data Layer)                       │
├─────────────────────────────────────────────────────────────┤
│  MySQL数据库    │  文件存储       │  缓存系统               │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 微服务架构

#### 核心服务模块

1. **用户服务 (User Service)**

   - 用户注册、登录、认证
   - 用户信息管理
   - 权限控制
2. **预约服务 (Reservation Service)**

   - 场地预约管理
   - 设备预约管理
   - 打印机预约管理
3. **审批服务 (Approval Service)**

   - 预约审批流程
   - 审批状态管理
   - 通知服务
4. **资源服务 (Resource Service)**

   - 资源信息管理
   - 可用性检查
   - 状态更新
5. **AI服务 (AI Service)**

   - 智能对话
   - 预约辅助
   - 自然语言处理

## 4. 数据库设计

### 4.1 数据库架构

```
MySQL数据库 (reservation_system)
├── 用户管理
│   ├── users (用户表)
│   └── user_sessions (会话表)
├── 预约管理
│   ├── venue_reservations (场地预约)
│   ├── device_reservations (设备预约)
│   └── printer_reservations (打印机预约)
├── 资源管理
│   ├── management (资源管理)
│   └── maintenance_records (维护记录)
└── 系统配置
    └── system_settings (系统设置)
```

### 4.2 核心数据表

![1748799170798](image/架构设计文档/1748799170798.png)

#### 用户表 (users)

```sql
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'teacher', 'admin') DEFAULT 'student',
    department VARCHAR(100),
    is_system_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 设备预约表 (device_reservations)

```sql
CREATE TABLE device_reservations (
    reservation_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    device_name VARCHAR(50) NOT NULL,
    borrow_time DATETIME NOT NULL,
    return_time DATETIME,
    actual_return_time DATETIME,
    reason TEXT,
    status ENUM('pending', 'approved', 'rejected', 'returned') DEFAULT 'pending',
    usage_type ENUM('onsite', 'takeaway') DEFAULT 'takeaway',
    teacher_name VARCHAR(100),
    device_condition ENUM('normal', 'damaged'),
    return_note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

## 5. 安全架构

### 5.1 认证机制

- **JWT Token**：基于JSON Web Token的无状态认证
- **Token过期机制**：自动刷新和过期处理
- **多端登录控制**：支持同一用户多设备登录

### 5.2 权限控制

```
角色权限矩阵：
┌─────────────┬─────────┬─────────┬─────────┐
│    功能     │  学生   │  教师   │ 管理员  │
├─────────────┼─────────┼─────────┼─────────┤
│ 提交预约    │   ✓     │   ✓     │   ✓     │
│ 查看预约    │   ✓     │   ✓     │   ✓     │
│ 审批预约    │   ✗     │   ✓     │   ✓     │
│ 用户管理    │   ✗     │   ✗     │   ✓     │
│ 系统设置    │   ✗     │   ✗     │   ✓     │
└─────────────┴─────────┴─────────┴─────────┘
```

### 5.3 数据安全

- **密码加密**：bcrypt哈希加密
- **SQL注入防护**：ORM参数化查询
- **XSS防护**：输入验证和输出编码
- **CSRF防护**：Token验证机制

## 6. 性能优化

### 6.1 数据库优化

- **索引优化**：关键字段建立索引
- **查询优化**：分页查询，避免全表扫描
- **连接池**：数据库连接池管理

### 6.2 缓存策略

- **应用缓存**：热点数据内存缓存
- **数据库缓存**：查询结果缓存
- **静态资源缓存**：CDN加速

### 6.3 并发处理

- **异步处理**：FastAPI异步特性
- **连接池**：数据库连接池
- **负载均衡**：多实例部署

## 7. 部署架构

### 7.1 容器化部署

```yaml
# docker-compose.yml 架构
services:
  fastapi-backend:    # FastAPI后端服务
  flask-admin:        # Flask管理系统
  mysql:             # MySQL数据库
  nginx:             # 反向代理（可选）
```

### 7.2 部署环境

- **开发环境**：本地Docker开发
- **测试环境**：Docker Compose部署
- **生产环境**：云服务器Docker部署

## 8. 监控与运维

### 8.1 日志管理

- **应用日志**：业务操作日志
- **错误日志**：异常和错误记录
- **访问日志**：API访问统计

### 8.2 监控指标

- **系统性能**：CPU、内存、磁盘使用率
- **应用性能**：响应时间、吞吐量
- **业务指标**：预约成功率、用户活跃度

## 9. 扩展性设计

### 9.1 水平扩展

- **无状态设计**：服务无状态，支持多实例
- **数据库分离**：读写分离，主从复制
- **微服务拆分**：按业务域拆分服务

### 9.2 功能扩展

- **插件机制**：支持功能模块插件化
- **API版本控制**：向后兼容的API设计
- **配置外部化**：环境变量配置管理

## 10. AI智能服务架构

### 10.1 AI服务设计

```
AI智能助手架构：
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户输入      │───▶│   意图识别      │───▶│   实体提取      │
│   (自然语言)    │    │   (NLU)         │    │   (NER)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   响应生成      │◀───│   业务逻辑      │◀───│   参数验证      │
│   (NLG)         │    │   (预约处理)    │    │   (数据校验)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 10.2 AI功能模块

- **自然语言理解**：解析用户预约意图
- **智能推荐**：基于历史数据推荐资源
- **自动填表**：智能提取预约信息
- **状态查询**：自然语言查询预约状态

## 11. 网络安全可视化

### 11.1 安全监控架构

```
网络安全可视化系统：
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API监控       │    │   流量分析      │    │   威胁检测      │
│   (实时监控)    │    │   (数据统计)    │    │   (异常识别)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   可视化展示     │
                    │   (3D城市模型)   │
                    └─────────────────┘
```

### 11.2 可视化组件

- **动态网络拓扑图**：实时显示API调用关系
- **性能仪表盘**：QPS、响应时间、错误率监控
- **3D城市模型**：建筑物高度表示API活跃度

## 12. 数据一致性设计

### 12.1 事务管理

- **ACID特性**：确保数据库事务完整性
- **分布式事务**：跨服务数据一致性保证
- **补偿机制**：失败回滚和数据恢复

### 12.2 并发控制

- **乐观锁**：版本号控制并发更新
- **悲观锁**：关键资源独占访问
- **分布式锁**：跨服务资源同步

## 13. 容错与高可用

### 13.1 故障处理

- **熔断器模式**：防止级联故障
- **重试机制**：自动重试失败请求
- **降级策略**：核心功能优先保障

### 13.2 备份恢复

- **数据备份**：定期自动备份
- **灾难恢复**：快速恢复机制
- **多地部署**：异地容灾备份

## 14. 技术债务与改进

### 14.1 当前限制

- 单体应用架构，耦合度较高
- 缺少专业的消息队列
- 监控和日志系统待完善
- AI功能相对简单，可扩展性有限

### 14.2 未来改进方向

- **架构升级**：微服务架构重构
- **中间件引入**：消息队列（RabbitMQ/Kafka）
- **监控完善**：Prometheus + Grafana监控体系
- **DevOps**：CI/CD流水线自动化
- **AI增强**：更智能的对话和推荐系统

## 15. 开发规范

### 15.1 代码规范

- **Python**：PEP 8编码规范
- **JavaScript**：ESLint代码检查
- **SQL**：命名规范和查询优化
- **API**：RESTful设计原则

### 15.2 版本控制

- **Git工作流**：Feature分支开发
- **代码审查**：Pull Request机制
- **版本标签**：语义化版本控制
- **文档同步**：代码与文档同步更新

---

**文档版本**：v1.2
**最后更新**：2025年6月
**维护人员**：系统开发团队
**审核人员**：技术负责人
