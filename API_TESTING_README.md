# 创新工坊预约系统API测试文档

## 📋 文档概览

本目录包含了创新工坊预约系统的完整API文档和测试工具：

### 📄 文档文件
- **`api_documentation.md`** - 完整的API接口文档
- **`API_SUMMARY.md`** - API接口总结和快速参考
- **`api_test_guide.md`** - 详细的API测试指南
- **`API_TESTING_README.md`** - 本文件，使用说明

### 🧪 测试工具
- **`simple_api_test.py`** - 简单的Python API测试脚本
- **`test_api.py`** - 完整的API测试脚本（包含文档生成）
- **`postman_collection.json`** - Postman测试集合

## 🚀 快速开始

### 1. 确认服务运行状态

首先确保系统服务正在运行：
- FastAPI服务：http://localhost:8001
- Flask管理系统：http://localhost:5000

### 2. 查看API文档

访问自动生成的API文档：
- Swagger UI：http://localhost:8001/docs
- 或查看本地文档：`api_documentation.md`

### 3. 运行简单测试

```bash
# 运行简单的API测试脚本
python simple_api_test.py
```

这个脚本会测试主要的API接口并显示结果。

## 🔧 测试工具使用

### Python测试脚本

#### 简单测试脚本 (`simple_api_test.py`)
```bash
python simple_api_test.py
```
- 测试基础功能
- 验证认证机制
- 检查主要接口状态
- 生成测试报告

#### 完整测试脚本 (`test_api.py`)
```bash
python test_api.py
```
- 全面的API测试
- 自动生成API文档
- 详细的错误报告
- 性能基准测试

### Postman集合

1. 打开Postman
2. 点击"Import"
3. 选择`postman_collection.json`文件
4. 导入后会看到完整的API测试集合

#### 使用步骤：
1. 首先运行"用户登录"请求获取token
2. token会自动保存到环境变量中
3. 其他需要认证的请求会自动使用这个token

### curl命令测试

#### 基础测试
```bash
# 测试根端点
curl -X GET "http://localhost:8001/"

# 用户登录
curl -X POST "http://localhost:8001/api/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=testpassword"

# 获取用户信息（替换YOUR_TOKEN）
curl -X GET "http://localhost:8001/api/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 测试场景

### 1. 基础功能测试
- [x] API服务可用性
- [x] 用户认证机制
- [x] 基础数据查询
- [x] 错误处理

### 2. 预约功能测试
- [x] 场地预约创建
- [x] 设备预约创建
- [x] 打印机预约创建
- [x] 预约记录查询
- [x] 时间冲突检测

### 3. 管理功能测试
- [x] 资源管理（设备/场地）
- [x] 预约审批流程
- [x] 用户管理
- [x] 数据导出

### 4. AI功能测试
- [x] AI服务状态检查
- [x] 智能对话功能
- [x] 功能开关控制

## 🔐 认证说明

### 测试账号
- **用户名**: testuser
- **密码**: testpassword
- **权限**: admin（具有管理员权限）

### JWT Token使用
1. 通过`/api/token`接口获取access_token
2. 在后续请求的Header中添加：
   ```
   Authorization: Bearer <access_token>
   ```
3. Token有效期为30分钟

## 📈 性能测试

### 使用Apache Bench
```bash
# 测试登录接口
ab -n 100 -c 10 -p login_data.txt -T "application/x-www-form-urlencoded" \
  http://localhost:8001/api/token

# 测试查询接口
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8001/api/reservations/my-reservations
```

### 使用Locust
```bash
# 运行项目中的性能测试
locust -f test/locustfile.py --host=http://localhost:8001
```

## 🐛 常见问题

### 1. 连接失败
- 确认服务是否启动
- 检查端口是否正确（8001/5000）
- 验证防火墙设置

### 2. 认证失败
- 确认用户名密码正确
- 检查token是否过期
- 验证Authorization头格式

### 3. 权限不足
- 确认用户角色权限
- 检查是否使用了正确的token
- 验证接口是否需要管理员权限

### 4. 数据验证错误
- 检查请求参数格式
- 验证日期时间格式
- 确认必填字段完整

## 📝 测试报告

### 生成测试报告
运行完整测试脚本后会生成：
- `api_docs.md` - 自动生成的API文档
- 控制台输出的测试结果
- 错误日志和性能数据

### 报告内容
- 接口可用性统计
- 响应时间分析
- 错误率统计
- 功能覆盖率

## 🔄 持续集成

### 自动化测试
可以将测试脚本集成到CI/CD流程中：

```yaml
# GitHub Actions示例
- name: Run API Tests
  run: |
    python simple_api_test.py
    python test_api.py
```

### 监控建议
- 定期运行健康检查
- 监控API响应时间
- 跟踪错误率变化
- 验证新功能兼容性

## 📞 支持

如果在测试过程中遇到问题：

1. 查看API文档：`api_documentation.md`
2. 参考测试指南：`api_test_guide.md`
3. 检查系统日志
4. 联系开发团队

## 📋 更新日志

- **v1.0.0** (2024-01-28)
  - 初始版本发布
  - 包含完整API文档
  - 提供多种测试工具
  - 支持Postman集合导入

---

**维护者**: 创新工坊开发团队  
**最后更新**: 2024年1月28日
