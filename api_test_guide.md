# 创新工坊预约系统API测试指南

## 测试环境

- **FastAPI服务**：http://localhost:8001
- **管理系统**：http://localhost:5000
- **API文档**：http://localhost:8001/docs
- **测试用户**：testuser / testpassword (admin权限)

## 使用工具测试

### 1. 使用curl命令测试

#### 测试根端点
```bash
curl -X GET "http://localhost:8001/" -H "accept: application/json"
```

#### 用户登录
```bash
curl -X POST "http://localhost:8001/api/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=testpassword"
```

#### 获取用户信息（需要先登录获取token）
```bash
curl -X GET "http://localhost:8001/api/users/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 使用Postman测试

#### 设置环境变量
- `base_url`: http://localhost:8001
- `access_token`: 登录后获取的token

#### 测试集合

**1. 认证测试**
- POST {{base_url}}/api/token
  - Body (form-data): username=testuser, password=testpassword
  - 保存返回的access_token到环境变量

**2. 用户信息测试**
- GET {{base_url}}/api/users/me
  - Headers: Authorization: Bearer {{access_token}}

**3. 场地预约测试**
- POST {{base_url}}/api/reservations/venue
  - Headers: Authorization: Bearer {{access_token}}
  - Body (JSON):
```json
{
  "venue_type": "会议室",
  "reservation_date": "2024-02-01",
  "business_time": "morning",
  "purpose": "团队会议",
  "devices_needed": {
    "screen": true,
    "laptop": false,
    "mic_handheld": true,
    "mic_gooseneck": false,
    "projector": true
  }
}
```

**4. 设备预约测试**
- POST {{base_url}}/api/reservations/device
  - Headers: Authorization: Bearer {{access_token}}
  - Body (JSON):
```json
{
  "device_name": "笔记本电脑",
  "borrow_time": "2024-02-01 09:00",
  "return_time": "2024-02-01 17:00",
  "reason": "项目开发",
  "usage_type": "takeaway",
  "teacher_name": "张老师"
}
```

**5. 打印机预约测试**
- POST {{base_url}}/api/reservations/printer
  - Headers: Authorization: Bearer {{access_token}}
  - Body (JSON):
```json
{
  "printer_name": "3D打印机A",
  "reservation_date": "2024-02-01",
  "print_time": "2024-02-01 10:00",
  "end_time": "2024-02-01 14:00",
  "estimated_duration": 240,
  "model_name": "手机壳模型",
  "teacher_name": "李老师"
}
```

### 3. 使用Python requests测试

```python
import requests
import json

# 基础配置
BASE_URL = "http://localhost:8001"
session = requests.Session()

# 1. 登录获取token
login_data = {
    "username": "testuser",
    "password": "testpassword"
}
response = session.post(
    f"{BASE_URL}/api/token",
    data=login_data,
    headers={"Content-Type": "application/x-www-form-urlencoded"}
)

if response.status_code == 200:
    result = response.json()
    token = result["access_token"]
    session.headers.update({"Authorization": f"Bearer {token}"})
    print("登录成功")
else:
    print("登录失败")

# 2. 获取用户信息
response = session.get(f"{BASE_URL}/api/users/me")
print("用户信息:", response.json())

# 3. 获取我的预约记录
response = session.get(f"{BASE_URL}/api/reservations/my-reservations")
print("预约记录:", response.json())

# 4. 创建场地预约
venue_data = {
    "venue_type": "会议室",
    "reservation_date": "2024-02-01",
    "business_time": "morning",
    "purpose": "测试预约",
    "devices_needed": {
        "screen": True,
        "laptop": False,
        "mic_handheld": True,
        "mic_gooseneck": False,
        "projector": True
    }
}
response = session.post(f"{BASE_URL}/api/reservations/venue", json=venue_data)
print("场地预约结果:", response.status_code, response.json())
```

## 管理员功能测试

### 1. 获取待审批预约
```bash
curl -X GET "http://localhost:8001/api/admin/reservations/pending" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 审批预约
```bash
curl -X POST "http://localhost:8001/api/admin/reservations/approve" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "venue",
    "id": 1,
    "status": "approved"
  }'
```

### 3. 获取用户列表
```bash
curl -X GET "http://localhost:8001/api/admin/users?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. 导出预约记录
```bash
curl -X GET "http://localhost:8001/api/admin/export-reservations" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  --output reservations.xlsx
```

## AI功能测试

### 1. 检查AI服务状态
```bash
curl -X GET "http://localhost:8001/api/ai/health"
```

### 2. AI聊天测试
```bash
curl -X POST "http://localhost:8001/api/ai/chat" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，我想预约一个会议室"
  }'
```

### 3. 设置AI功能状态
```bash
# 启用AI功能
curl -X POST "http://localhost:8001/api/settings/ai-feature" \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'

# 禁用AI功能
curl -X POST "http://localhost:8001/api/settings/ai-feature" \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'
```

## 常见测试场景

### 1. 完整预约流程测试
1. 用户登录
2. 查看可用场地/设备
3. 创建预约
4. 管理员审批
5. 用户查看预约状态

### 2. 冲突检测测试
1. 创建场地预约（时间段A）
2. 尝试创建相同场地相同时间段的预约
3. 验证系统是否正确阻止冲突

### 3. 权限测试
1. 使用普通用户token访问管理员接口
2. 验证是否返回403权限错误

### 4. 数据验证测试
1. 发送无效的日期格式
2. 发送缺少必填字段的请求
3. 验证系统是否返回适当的错误信息

## 性能测试

### 使用Apache Bench (ab)
```bash
# 测试登录接口性能
ab -n 100 -c 10 -p login_data.txt -T "application/x-www-form-urlencoded" \
  http://localhost:8001/api/token

# 测试获取预约记录接口性能（需要先获取token）
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8001/api/reservations/my-reservations
```

### 使用Locust进行负载测试
项目中已包含locustfile.py，可以运行：
```bash
locust -f test/locustfile.py --host=http://localhost:8001
```

## 错误处理测试

### 1. 测试无效token
```bash
curl -X GET "http://localhost:8001/api/users/me" \
  -H "Authorization: Bearer invalid_token"
```

### 2. 测试缺少认证头
```bash
curl -X GET "http://localhost:8001/api/users/me"
```

### 3. 测试无效请求数据
```bash
curl -X POST "http://localhost:8001/api/reservations/venue" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "venue_type": "",
    "reservation_date": "invalid-date"
  }'
```

## 测试数据清理

测试完成后，可以通过管理系统或直接操作数据库清理测试数据：

1. 删除测试预约记录
2. 重置设备可用数量
3. 清理测试用户数据

## 自动化测试脚本

项目包含以下测试脚本：
- `test_api.py`: 完整的API测试脚本
- `test/locustfile.py`: 性能测试脚本
- `test/bulk_reservation_test.py`: 批量预约测试

运行方式：
```bash
python test_api.py
```

## 注意事项

1. 测试前确保服务正在运行
2. 使用测试用户进行测试，避免影响生产数据
3. 测试完成后及时清理测试数据
4. 注意API限流和并发限制
5. 测试时注意时区和日期格式
6. 文件上传测试需要准备相应格式的测试文件
