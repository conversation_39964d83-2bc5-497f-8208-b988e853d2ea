/* pages/my/my.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F6 0%, #E6E9FF 100%);
  padding: 48rpx 32rpx;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.container::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 60%);
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.login-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 64rpx 40rpx;
  border-radius: 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  position: relative;
  backdrop-filter: blur(10px);
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 48rpx;
  opacity: 0.9;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.form-group {
  display: flex;
  align-items: center;
  background: rgba(245, 247, 250, 0.8);
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.form-group::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.2), transparent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.form-group:focus-within {
  background: rgba(235, 240, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.1);
}

.form-group:focus-within::before {
  transform: scaleX(1);
}

.form-group image {
  width: 44rpx;
  height: 44rpx;
  margin-right: 24rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.form-group:focus-within image {
  opacity: 0.8;
  transform: scale(1.1);
}

.form-group input {
  flex: 1;
  font-size: 30rpx;
  color: #1A1A1A;
  padding: 8rpx 0;
}

.login-btn {
  margin: 64rpx auto 0;
  width: 85%;
  background: linear-gradient(135deg, #007AFF, #00A6FF);
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border-radius: 16rpx;
  letter-spacing: 2rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.2);
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s;
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.login-btn:active::before {
  transform: translateX(100%);
}

/* 添加底部文字样式 */
.login-tips {
  margin-top: 32rpx;
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

/* 添加输入框提示动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

.form-group.error {
  animation: shake 0.3s ease-in-out;
  border: 1rpx solid rgba(255, 59, 48, 0.3);
}

/* 添加加载动画 */
.login-btn.loading {
  position: relative;
  pointer-events: none;
}

.login-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 32rpx;
  margin: -16rpx 0 0 -16rpx;
  border: 3rpx solid #fff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 添加成功动画 */
.login-btn.success {
  background: linear-gradient(135deg, #34C759, #30D158);
}

.login-btn.success::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 36rpx;
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.user-section {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.user-info {
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #007AFF, #00A6FF);
  position: relative;
  overflow: hidden;
}

.user-info::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shine 3s infinite;
}

.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.avatar:active {
  transform: scale(0.95);
}

.info {
  color: #fff;
}

.name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.role {
  font-size: 24rpx;
  opacity: 0.9;
}

.menu-list {
  padding: 8rpx 24rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
}

.menu-item:active {
  background: #F5F7FA;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-left image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
  opacity: 0.7;
}

.menu-left text {
  font-size: 28rpx;
  color: #1A1A1A;
}

.arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}