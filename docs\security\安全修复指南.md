# 安全修复实施指南

## 🎯 修复目标

本指南提供了针对网络安全隐患分析报告中发现问题的具体修复步骤和代码示例。

## 🚨 高优先级修复 (立即执行)

### 1. JWT密钥环境变量化

**修复文件**：`app/routers/auth.py`, `app/utils/auth.py`

**当前问题**：
```python
SECRET_KEY = "your-secret-key"  # 硬编码
```

**修复步骤**：

1. 生成安全密钥：
```bash
# 生成32字节随机密钥
python -c "import secrets; print(secrets.token_hex(32))"
```

2. 更新环境变量文件：
```bash
# 在.env文件中添加
JWT_SECRET_KEY=your_generated_secure_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
```

3. 修改代码：
```python
# app/utils/auth.py
import os
from dotenv import load_dotenv

load_dotenv()

SECRET_KEY = os.getenv("JWT_SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("JWT_SECRET_KEY environment variable is required")

ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_EXPIRE_MINUTES", "30"))
```

### 2. CORS策略限制

**修复文件**：`app/main.py`

**当前问题**：
```python
allow_origins=["*"]  # 过于宽松
```

**修复代码**：
```python
# app/main.py
import os

# 从环境变量获取允许的域名
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5000").split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],  # 限制方法
    allow_headers=["Authorization", "Content-Type"],  # 限制头部
)
```

**环境变量配置**：
```bash
# .env
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,https://yourdomain.com
```

### 3. API密钥安全管理

**修复文件**：`app/routers/ai.py`

**当前问题**：API密钥直接暴露在.env文件中

**修复步骤**：

1. 轮换API密钥（在DeepSeek控制台）
2. 使用密钥管理服务或加密存储
3. 添加密钥验证：

```python
# app/routers/ai.py
import os
import logging

logger = logging.getLogger(__name__)

def validate_api_key():
    """验证API密钥是否配置正确"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        logger.error("DEEPSEEK_API_KEY not configured")
        return False
    
    if api_key.startswith("sk-") and len(api_key) > 20:
        return True
    
    logger.error("Invalid DEEPSEEK_API_KEY format")
    return False

# 在应用启动时验证
if not validate_api_key():
    raise ValueError("Invalid API key configuration")
```

## ⚠️ 中优先级修复 (1-2周内)

### 4. 输入验证加强

**创建验证模块**：`app/utils/validators.py`

```python
# app/utils/validators.py
import re
from typing import Optional
from fastapi import HTTPException

class InputValidator:
    @staticmethod
    def validate_username(username: str) -> str:
        """验证用户名格式"""
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
            raise HTTPException(400, "用户名只能包含字母、数字和下划线，长度3-20位")
        return username.strip()
    
    @staticmethod
    def validate_password(password: str) -> str:
        """验证密码强度"""
        if len(password) < 6:
            raise HTTPException(400, "密码长度至少6位")
        return password
    
    @staticmethod
    def validate_file_upload(filename: str, content_type: str, size: int) -> bool:
        """验证文件上传"""
        # 允许的文件类型
        ALLOWED_TYPES = {
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        }
        
        # 允许的文件扩展名
        ALLOWED_EXTENSIONS = {'.xlsx', '.xls'}
        
        # 最大文件大小 (10MB)
        MAX_SIZE = 10 * 1024 * 1024
        
        if content_type not in ALLOWED_TYPES:
            raise HTTPException(400, "不支持的文件类型")
        
        if not any(filename.lower().endswith(ext) for ext in ALLOWED_EXTENSIONS):
            raise HTTPException(400, "不支持的文件扩展名")
        
        if size > MAX_SIZE:
            raise HTTPException(400, "文件大小超过限制(10MB)")
        
        return True
```

**应用验证器**：
```python
# app/routers/auth.py
from ..utils.validators import InputValidator

@router.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    # 验证输入
    username = InputValidator.validate_username(form_data.username)
    password = InputValidator.validate_password(form_data.password)
    
    # 其余登录逻辑...
```

### 5. 文件上传安全

**修复文件**：`app/routers/admin.py`

```python
# app/routers/admin.py
from ..utils.validators import InputValidator

@router.post("/users/import")
async def import_users(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_admin)
):
    try:
        # 验证文件
        InputValidator.validate_file_upload(
            filename=file.filename,
            content_type=file.content_type,
            size=len(await file.read())
        )
        
        # 重置文件指针
        await file.seek(0)
        
        # 读取文件内容
        contents = await file.read()
        
        # 病毒扫描 (可选)
        # scan_result = virus_scanner.scan(contents)
        # if not scan_result.is_clean:
        #     raise HTTPException(400, "文件包含恶意内容")
        
        # 处理文件...
        
    except Exception as e:
        logger.error(f"File upload error: {str(e)}")
        raise HTTPException(500, "文件处理失败")
```

### 6. API限流实施

**安装依赖**：
```bash
pip install slowapi
```

**实施限流**：
```python
# app/main.py
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# 创建限流器
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# app/routers/auth.py
from slowapi import Limiter
from fastapi import Request

@router.post("/token")
@limiter.limit("5/minute")  # 每分钟最多5次登录尝试
async def login(request: Request, form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    # 登录逻辑...
```

### 7. 安全日志记录

**创建安全日志模块**：`app/utils/security_logger.py`

```python
# app/utils/security_logger.py
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class SecurityLogger:
    def __init__(self):
        self.logger = logging.getLogger('security')
        self.logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        handler = logging.FileHandler('logs/security.log')
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_event(self, event_type: str, user_id: Optional[str] = None, 
                  details: Optional[Dict[str, Any]] = None, severity: str = "INFO"):
        """记录安全事件"""
        event_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'user_id': self._mask_sensitive_data(user_id),
            'details': details or {},
            'severity': severity
        }
        
        log_message = json.dumps(event_data, ensure_ascii=False)
        
        if severity == "ERROR":
            self.logger.error(log_message)
        elif severity == "WARNING":
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _mask_sensitive_data(self, data: Optional[str]) -> Optional[str]:
        """脱敏处理敏感数据"""
        if not data:
            return None
        
        if len(data) > 4:
            return data[:2] + "*" * (len(data) - 4) + data[-2:]
        return "*" * len(data)

# 全局实例
security_logger = SecurityLogger()
```

**使用安全日志**：
```python
# app/routers/auth.py
from ..utils.security_logger import security_logger

@router.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    try:
        # 记录登录尝试
        security_logger.log_event(
            event_type="login_attempt",
            user_id=form_data.username,
            details={"ip": request.client.host}
        )
        
        # 验证用户...
        if not user:
            security_logger.log_event(
                event_type="login_failed",
                user_id=form_data.username,
                details={"reason": "user_not_found"},
                severity="WARNING"
            )
            raise HTTPException(401, "用户名或密码错误")
        
        # 验证密码...
        if not verify_password(form_data.password, user.password):
            security_logger.log_event(
                event_type="login_failed",
                user_id=form_data.username,
                details={"reason": "invalid_password"},
                severity="WARNING"
            )
            raise HTTPException(401, "用户名或密码错误")
        
        # 登录成功
        security_logger.log_event(
            event_type="login_success",
            user_id=user.username,
            details={"role": user.role}
        )
        
        # 生成token...
        
    except HTTPException:
        raise
    except Exception as e:
        security_logger.log_event(
            event_type="login_error",
            user_id=form_data.username,
            details={"error": str(e)},
            severity="ERROR"
        )
        raise HTTPException(500, "登录失败")
```

## 💡 低优先级修复 (2-4周内)

### 8. 生产环境配置

**修复文件**：`app/database.py`

```python
# app/database.py
import os

# 根据环境变量控制调试输出
DEBUG_MODE = os.getenv("DEBUG", "false").lower() == "true"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=DEBUG_MODE,  # 只在调试模式下启用
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=10,
    max_overflow=20
)
```

### 9. 密码策略加强

**修复文件**：`docker-compose.yml`

```yaml
# docker-compose.yml
services:
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-$(openssl rand -base64 32)}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-$(openssl rand -base64 32)}
```

**环境变量**：
```bash
# .env
MYSQL_ROOT_PASSWORD=your_complex_password_here
MYSQL_PASSWORD=your_complex_password_here
```

## 🔧 部署检查清单

### 部署前检查

- [ ] 所有敏感信息已移至环境变量
- [ ] .env文件已添加到.gitignore
- [ ] CORS策略已限制为具体域名
- [ ] API限流已启用
- [ ] 文件上传验证已实施
- [ ] 安全日志记录已配置
- [ ] 生产环境调试模式已关闭

### 部署后验证

- [ ] JWT token正常工作
- [ ] API限流生效
- [ ] 文件上传限制生效
- [ ] 安全日志正常记录
- [ ] CORS策略正确限制
- [ ] 数据库连接正常

## 📊 安全测试

### 手动测试

```bash
# 测试API限流
for i in {1..10}; do
  curl -X POST http://localhost:8001/api/token \
    -d "username=test&password=test" \
    -H "Content-Type: application/x-www-form-urlencoded"
done

# 测试CORS策略
curl -X OPTIONS http://localhost:8001/api/users/me \
  -H "Origin: http://malicious-site.com" \
  -H "Access-Control-Request-Method: GET"

# 测试文件上传限制
curl -X POST http://localhost:8001/api/admin/users/import \
  -F "file=@large_file.txt" \
  -H "Authorization: Bearer your_token"
```

### 自动化安全扫描

```bash
# 安装安全扫描工具
pip install bandit safety

# 代码安全扫描
bandit -r app/ -f json -o security_report.json

# 依赖漏洞扫描
safety check -r requirements.txt --json > dependency_report.json

# Docker镜像安全扫描
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image reservation-system:latest
```

## 📞 支持与维护

### 定期维护任务

1. **每周**：检查安全日志异常
2. **每月**：更新依赖包，扫描漏洞
3. **每季度**：轮换密钥，安全评估
4. **每年**：全面渗透测试

### 应急响应

如发现安全事件：
1. 立即隔离受影响系统
2. 收集和保存证据
3. 评估影响范围
4. 实施修复措施
5. 更新安全策略

---

*本指南应与网络安全隐患分析报告配合使用，定期更新以适应新的安全威胁。*
