# Docker启动指南

## 🐳 **Docker配置说明**

### 密码配置
系统已配置统一密码：`123456`

- **MySQL root密码**: `123456`
- **MySQL用户密码**: `123456`
- **应用连接密码**: `123456`

### 服务架构
```
┌─────────────────┐    ┌─────────────────┐
│   MySQL 8.0     │    │ Reservation App │
│   Port: 3306    │◄───┤ Port: 8001,5000 │
│   Password:123456│    │                 │
└─────────────────┘    └─────────────────┘
```

## 🚀 **启动方式**

### 方法一：完整启动（推荐）
```bash
# 启动所有服务（MySQL + 应用）
docker-compose up

# 后台运行
docker-compose up -d
```

### 方法二：分步启动
```bash
# 1. 先启动MySQL
docker-compose up mysql -d

# 2. 等待30秒让MySQL完全启动

# 3. 启动应用
docker-compose up reservation-system
```

### 方法三：重新构建启动
```bash
# 如果代码有更新，重新构建
docker-compose up --build
```

## 📋 **启动流程详解**

### 1. MySQL容器启动
- 拉取 `mysql:8.0` 镜像
- 创建数据库：`reservation_system` 和 `admin_system`
- 设置root密码：`123456`
- 创建用户：`reservation_user`，密码：`123456`
- 执行初始化脚本：`init-mysql.sql`

### 2. 应用容器启动
- 构建应用镜像（基于Dockerfile.backend）
- 等待MySQL容器就绪
- 连接MySQL数据库
- 初始化数据库表结构
- 启动FastAPI（端口8001）和Flask（端口5000）

## 🔧 **环境变量配置**

### Docker环境变量
```yaml
environment:
  - TZ=Asia/Shanghai          # 时区设置
  - PYTHONPATH=/app          # Python路径
  - MYSQL_HOST=mysql         # MySQL主机（容器名）
  - MYSQL_PORT=3306          # MySQL端口
  - MYSQL_USER=root          # MySQL用户
  - MYSQL_PASSWORD=123456    # MySQL密码
  - MYSQL_DATABASE=reservation_system
  - ADMIN_MYSQL_DATABASE=admin_system
```

### .env文件配置
```env
# MySQL数据库配置
MYSQL_HOST=localhost        # 本地开发用localhost，Docker用mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=reservation_system
ADMIN_MYSQL_DATABASE=admin_system
```

## 📁 **数据持久化**

### 数据卷配置
```yaml
volumes:
  mysql-data:               # MySQL数据持久化
    driver: local
  app-data:                 # 应用数据持久化
    driver: local
```

### 挂载点
- MySQL数据：`mysql-data:/var/lib/mysql`
- 应用数据：`app-data:/app`
- 环境配置：`./.env:/app/.env`
- 初始化脚本：`./init-mysql.sql:/docker-entrypoint-initdb.d/init-mysql.sql`

## 🌐 **访问地址**

启动成功后可访问：
- **FastAPI后端**: http://localhost:8001
- **API文档**: http://localhost:8001/docs
- **管理系统**: http://localhost:5000
- **MySQL数据库**: localhost:3306

## 🔍 **常用Docker命令**

### 查看状态
```bash
# 查看运行中的容器
docker-compose ps

# 查看日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs reservation-system
```

### 管理操作
```bash
# 停止服务
docker-compose stop

# 停止并删除容器
docker-compose down

# 停止并删除容器和数据卷（危险！会删除数据）
docker-compose down -v

# 重启服务
docker-compose restart
```

### 进入容器
```bash
# 进入MySQL容器
docker-compose exec mysql bash

# 进入应用容器
docker-compose exec reservation-system bash

# 连接MySQL数据库
docker-compose exec mysql mysql -u root -p123456
```

## 🛠 **故障排除**

### 1. 端口冲突
如果端口被占用，修改docker-compose.yml中的端口映射：
```yaml
ports:
  - "8002:8001"  # 改为8002
  - "5001:5000"  # 改为5001
```

### 2. MySQL启动失败
```bash
# 查看MySQL日志
docker-compose logs mysql

# 删除数据卷重新开始
docker-compose down -v
docker-compose up
```

### 3. 应用连接数据库失败
```bash
# 检查MySQL是否就绪
docker-compose exec mysql mysql -u root -p123456 -e "SHOW DATABASES;"

# 重启应用容器
docker-compose restart reservation-system
```

### 4. 数据库初始化问题
```bash
# 手动执行初始化
docker-compose exec mysql mysql -u root -p123456 < /docker-entrypoint-initdb.d/init-mysql.sql
```

## 📝 **开发模式**

### 本地开发 + Docker MySQL
```bash
# 只启动MySQL
docker-compose up mysql -d

# 本地运行应用（.env中MYSQL_HOST=localhost）
python runme.py
```

### 完全Docker模式
```bash
# 启动所有服务
docker-compose up

# 代码修改后重新构建
docker-compose up --build
```

## 🔐 **安全注意事项**

1. **生产环境密码**: 请修改默认密码`123456`
2. **网络安全**: 生产环境建议不暴露MySQL端口3306
3. **数据备份**: 定期备份mysql-data数据卷
4. **环境变量**: 敏感信息使用Docker secrets

启动Docker后，系统会自动处理数据库连接和初始化！
