# 创新工坊预约系统 - 环境配置指南

本指南介绍如何使用宝塔面板部署创新工坊预约系统，包括服务器环境配置、域名设置、SSL证书申请等。

## 目录

1. [服务器基础环境配置](#服务器基础环境配置)
2. [宝塔面板安装](#宝塔面板安装)
3. [Python环境配置](#python环境配置)
4. [Git配置](#git配置)
5. [域名配置](#域名配置)
6. [宝塔面板网站配置](#宝塔面板网站配置)
7. [SSL证书配置](#ssl证书配置)
8. [反向代理配置](#反向代理配置)
9. [项目部署](#项目部署)
10. [微信小程序配置](#微信小程序配置)
11. [监控与维护](#监控与维护)

## 服务器基础环境配置

### 更新系统包

```bash
# 更新包列表
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install wget curl git vim -y
```

### 安装Python环境

```bash
# 安装Python相关工具
sudo apt install python3 python3-pip python3-venv python3-dev build-essential -y

# 验证安装
python3 --version
pip3 --version

# 升级pip
python3 -m pip install --upgrade pip
```

## 宝塔面板安装

### Ubuntu/Debian系统安装宝塔面板

```bash
# 下载并安装宝塔面板
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### CentOS系统安装宝塔面板

```bash
# 下载并安装宝塔面板
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

### 安装完成后

1. 记录安装完成后显示的面板地址、用户名和密码
2. 在浏览器中访问面板地址进行初始化配置
3. 安装推荐的软件组合：Nginx + MySQL + PHP + phpMyAdmin

## Python环境配置

在宝塔面板中配置Python环境：

1. 进入宝塔面板 -> 软件商店
2. 搜索并安装 "Python项目管理器"
3. 或者手动配置Python环境：

```bash
# 创建项目目录
mkdir -p /www/wwwroot/newinno
cd /www/wwwroot/newinno

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装项目依赖（后续部署时执行）
# pip install -r requirements.txt
```

## Git配置

配置Git用于代码管理：

```bash
# 配置全局用户名和邮箱
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 查看配置
git config --list

# 生成SSH密钥（可选，用于GitHub/GitLab）
ssh-keygen -t ed25519 -C "<EMAIL>"

# 启动ssh-agent并添加密钥
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519

# 查看公钥（需要添加到GitHub/GitLab）
cat ~/.ssh/id_ed25519.pub

# 测试连接
ssh -T **************
```

## 域名配置

### 1. 购买域名

- 在阿里云/腾讯云/其他域名服务商购买域名（如：yourdomain.com）
- 如果是国内服务器，需要进行域名备案（预计1-2周）

### 2. 域名解析配置

在域名服务商控制台添加DNS解析记录：

```
记录类型: A
主机记录: api          # 这样API域名就是 api.yourdomain.com
记录值: 你的服务器IP地址
TTL: 600秒
```

如果需要支持根域名访问，还可以添加：

```
记录类型: A
主机记录: @           # 根域名 yourdomain.com
记录值: 你的服务器IP地址
TTL: 600秒
```

### 3. 验证域名解析

```bash
# 检查域名解析是否生效
nslookup api.yourdomain.com
ping api.yourdomain.com
```

## 宝塔面板网站配置

### 1. 创建网站

1. 登录宝塔面板
2. 点击 "网站" -> "添加站点"
3. 填写域名：`api.yourdomain.com`
4. 选择PHP版本：无需PHP（我们使用Python）
5. 创建数据库：可选，如果需要MySQL数据库
6. 点击提交创建

### 2. 配置网站目录

1. 在网站列表中找到刚创建的网站
2. 点击 "设置"
3. 在 "网站目录" 中设置为：`/www/wwwroot/newinno`
4. 关闭 "防跨站攻击" （因为我们使用API服务）

## SSL证书配置

### 使用宝塔面板申请Let's Encrypt证书

1. 在网站设置中点击 "SSL" 选项卡
2. 选择 "Let's Encrypt"
3. 勾选你的域名（如：api.yourdomain.com）
4. 点击 "申请" 按钮
5. 等待证书申请完成
6. 开启 "强制HTTPS" 选项

### 手动上传证书（可选）

如果你有其他SSL证书：

1. 在SSL选项卡中选择 "其他证书"
2. 将证书内容粘贴到对应框中：
   - 证书(PEM格式)
   - 密钥(KEY)
3. 点击保存
4. 开启 "强制HTTPS"

## 反向代理配置

### 在宝塔面板中配置反向代理

1. 在网站设置中点击 "反向代理" 选项卡
2. 点击 "添加反向代理"
3. 填写配置：
   - 代理名称：`FastAPI Backend`
   - 目标URL：`http://127.0.0.1:8001`
   - 发送域名：`$host`
   - 内容替换：留空
4. 点击提交

### 高级配置（可选）

如果需要更详细的配置，可以点击 "配置文件" 手动编辑Nginx配置：

```nginx
location / {
    proxy_pass http://127.0.0.1:8001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
}
```

## 项目部署

### 1. 上传项目代码

```bash
# 进入项目目录
cd /www/wwwroot/newinno

# 克隆项目（如果使用Git）
git clone https://github.com/yourusername/newinno.git .

# 或者直接上传项目文件到该目录
```

### 2. 配置Python虚拟环境

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装项目依赖
pip install -r requirements.txt

# 如果没有requirements.txt，手动安装主要依赖
pip install fastapi uvicorn sqlalchemy python-multipart python-jose[cryptography] passlib[bcrypt]
```

### 3. 配置数据库

如果使用SQLite（默认）：
```bash
# 确保数据库文件权限正确
chmod 664 *.db
chown www:www *.db
```

如果使用MySQL：
1. 在宝塔面板中创建数据库
2. 修改项目配置文件中的数据库连接信息

### 4. 启动应用服务

#### 方法一：使用宝塔面板的Python项目管理器

1. 在宝塔面板中安装 "Python项目管理器"
2. 添加项目：
   - 项目名称：newinno
   - 项目路径：/www/wwwroot/newinno
   - 启动文件：runme.py 或 main.py
   - 端口：8001
3. 启动项目

#### 方法二：手动启动（临时测试）

```bash
# 进入项目目录
cd /www/wwwroot/newinno

# 激活虚拟环境
source venv/bin/activate

# 启动应用
python runme.py
# 或者
uvicorn main:app --host 0.0.0.0 --port 8001
```

#### 方法三：配置系统服务（推荐生产环境）

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/newinno.service
```

服务文件内容：

```ini
[Unit]
Description=NewInno FastAPI application
After=network.target

[Service]
User=www
Group=www
WorkingDirectory=/www/wwwroot/newinno
Environment="PATH=/www/wwwroot/newinno/venv/bin"
ExecStart=/www/wwwroot/newinno/venv/bin/python runme.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start newinno

# 检查服务状态
sudo systemctl status newinno

# 设置开机自启
sudo systemctl enable newinno
```

## 微信小程序配置

### 1. 修改小程序请求地址

修改前端请求配置文件：

```javascript
// fore/utils/request.js
const baseURL = 'https://api.yourdomain.com/api'  // 使用HTTPS域名
```

### 2. 微信公众平台配置

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入 "开发管理" -> "开发设置"
3. 在 "服务器域名" 下添加：
   ```
   request合法域名: https://api.yourdomain.com
   uploadFile合法域名: https://api.yourdomain.com
   downloadFile合法域名: https://api.yourdomain.com
   ```

### 3. 上传小程序代码

1. 在微信开发者工具中取消勾选 "不校验合法域名"
2. 测试所有功能正常后，点击 "上传"
3. 在微信公众平台提交审核

## 监控与维护

### 1. 服务状态检查

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查应用服务状态
sudo systemctl status newinno

# 检查端口占用
sudo netstat -tulpn | grep -E '80|443|8001'
```

### 2. 日志查看

```bash
# 查看Nginx访问日志
tail -f /www/wwwlogs/api.yourdomain.com.log

# 查看Nginx错误日志
tail -f /www/wwwlogs/api.yourdomain.com.error.log

# 查看应用日志
sudo journalctl -u newinno -f
```

### 3. 宝塔面板监控

1. 在宝塔面板中查看系统状态
2. 设置监控报警（CPU、内存、磁盘使用率）
3. 配置定时任务进行数据备份

### 4. 安全配置

#### 防火墙设置

在宝塔面板的安全设置中：
1. 开放端口：80, 443, 8888（宝塔面板端口）
2. 禁用不必要的端口
3. 设置SSH端口（建议修改默认22端口）

#### SSL证书自动续期

Let's Encrypt证书会自动续期，但建议定期检查：

```bash
# 检查证书有效期
openssl x509 -in /www/server/panel/vhost/cert/api.yourdomain.com/fullchain.pem -noout -dates
```

### 5. 数据备份

#### 自动备份脚本

创建备份脚本：

```bash
#!/bin/bash
# 备份脚本 /root/backup_newinno.sh

BACKUP_DIR="/www/backup/newinno"
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_DIR="/www/wwwroot/newinno"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库文件（如果使用SQLite）
if [ -f "$PROJECT_DIR/app.db" ]; then
    cp "$PROJECT_DIR/app.db" "$BACKUP_DIR/app_$DATE.db"
fi

# 备份项目文件
tar -czf "$BACKUP_DIR/project_$DATE.tar.gz" -C "$PROJECT_DIR" .

# 删除7天前的备份
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

设置定时任务：

```bash
# 编辑crontab
crontab -e

# 添加每日凌晨2点备份
0 2 * * * /root/backup_newinno.sh >> /var/log/backup.log 2>&1
```

### 6. 性能优化

1. **启用Gzip压缩**：在宝塔面板网站设置中开启
2. **配置缓存**：设置静态文件缓存时间
3. **数据库优化**：定期清理日志，优化查询
4. **监控资源使用**：通过宝塔面板监控CPU、内存使用情况

### 7. 故障排除

#### 常见问题

1. **502 Bad Gateway**：
   - 检查后端服务是否正常运行
   - 检查端口8001是否被占用
   - 查看Nginx错误日志

2. **SSL证书问题**：
   - 检查证书是否过期
   - 重新申请Let's Encrypt证书
   - 检查域名解析是否正确

3. **服务无法启动**：
   - 检查Python虚拟环境
   - 检查依赖包是否完整
   - 查看应用日志

#### 紧急恢复

如果服务出现问题，可以快速恢复：

```bash
# 重启所有相关服务
sudo systemctl restart nginx
sudo systemctl restart newinno

# 如果使用宝塔面板Python项目管理器
# 在面板中重启项目即可
```

## 总结

使用宝塔面板部署的优势：
- 图形化界面，操作简单
- 自动化SSL证书管理
- 集成监控和日志查看
- 一键备份和恢复
- 安全防护功能

完成以上配置后，你的创新工坊预约系统就可以正式上线运行了。记得定期检查系统状态、更新安全补丁、备份重要数据。
