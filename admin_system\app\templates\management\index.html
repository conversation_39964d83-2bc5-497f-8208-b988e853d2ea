{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>设备和场地管理</h2>
    
    <div class="mb-3">
        <a href="{{ url_for('admin.add_management') }}" class="btn btn-primary">添加新项目</a>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h4>场地管理</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for venue in venues %}
                    <tr>
                        <td>{{ venue.device_or_venue_name }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_management', id=venue.management_id) }}" class="btn btn-sm btn-info">编辑</a>
                            <form action="{{ url_for('admin.delete_management', id=venue.management_id) }}" method="POST" style="display: inline;">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除吗？')">删除</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4>设备管理</h4>
        </div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for device in devices %}
                    <tr>
                        <td>{{ device.device_or_venue_name }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_management', id=device.management_id) }}" class="btn btn-sm btn-info">编辑</a>
                            <form action="{{ url_for('admin.delete_management', id=device.management_id) }}" method="POST" style="display: inline;">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除吗？')">删除</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 