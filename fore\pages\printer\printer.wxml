<!--pages/printer/printer.wxml-->
<view class="container">
  <view class="printer-list">
    <view class="printer-item" wx:for="{{printers}}" wx:key="id">
      <view class="printer-info">
        <image class="printer-icon" src="https://img.icons8.com/windows/32/3d-printer.png"/>
        <text class="printer-name">{{item.name}}</text>
        <text class="printer-status available">可预约</text>
      </view>
      <button class="reserve-btn" 
              bindtap="goToReserve" 
              data-printer="{{item.id}}">
        预约
      </button>
    </view>
  </view>

  <view class="reservation-form" wx:if="{{showForm}}">
    <view class="form-header">
      <image class="back-icon" bindtap="hideForm" src="https://img.icons8.com/windows/32/back.png"/>
      <text>预约{{selectedPrinter.name}}</text>
    </view>

    <view class="form-group">
      <view class="form-item">
        <text class="label">预约日期</text>
        <picker mode="date" value="{{date}}" start="{{minDate}}" bindchange="handleDateChange">
          <view class="picker {{date ? '' : 'placeholder'}}">
            <text wx:if="{{date}}">{{date}}</text>
            <text wx:else class="placeholder-text">请选择日期</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">开始时间</text>
        <picker mode="time" value="{{startTime}}" bindchange="handleStartTimeChange">
          <view class="picker {{startTime ? '' : 'placeholder'}}">
            <text wx:if="{{startTime}}">{{startTime}}</text>
            <text wx:else class="placeholder-text">请选择开始时间</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">结束时间</text>
        <picker mode="time" value="{{endTime}}" bindchange="handleEndTimeChange">
          <view class="picker {{endTime ? '' : 'placeholder'}}">
            <text wx:if="{{endTime}}">{{endTime}}</text>
            <text wx:else class="placeholder-text">请选择结束时间</text>
            <view class="picker-arrow"></view>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">预计打印耗时(分钟)</text>
        <view class="input-container">
          <input type="number" value="{{duration}}" bindinput="handleDurationChange" placeholder="请输入预计打印耗时" placeholder-class="placeholder-text" />
        </view>
      </view>

      <view class="form-item">
        <text class="label">打印模型名称</text>
        <view class="input-container">
          <input type="text" value="{{modelName}}" bindinput="handleModelNameChange" placeholder="请输入模型名称" placeholder-class="placeholder-text" />
        </view>
      </view>

      <view class="form-item">
        <text class="label">指导老师</text>
        <view class="input-container">
          <input type="text" value="{{teacherName}}" bindinput="handleTeacherNameChange" placeholder="请输入指导老师姓名" placeholder-class="placeholder-text" />
        </view>
      </view>
    </view>

    <button class="submit-btn" type="primary" bindtap="handleSubmit">提交预约</button>
  </view>
</view>