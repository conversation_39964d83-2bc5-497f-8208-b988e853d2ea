/* pages/device/device.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F2F6 0%, #F7F8FA 100%);
  padding: 24rpx;
}

/* 表单样式 */
.form-group {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.form-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  min-width: 180rpx;
  max-width: 240rpx;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: visible;
  font-weight: 500;
  padding-right: 30rpx;
  padding-top: 16rpx;
}

.value-container {
  flex: 1;
  background: #F7F8FA;
  padding: 18rpx 24rpx;
  border-radius: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #666;
}

.picker {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  background: #F7F8FA;
  padding: 18rpx 24rpx;
  border-radius: 8rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder-text {
  color: #999;
}

.picker-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

/* 单选框容器 */
.radio-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.radio-item:last-child {
  margin-bottom: 0;
}

/* 文本框样式 */
.textarea-container {
  flex: 1;
  background: #F7F8FA;
  border-radius: 8rpx;
  padding: 0 24rpx;
}

textarea {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  color: #666;
  padding: 24rpx 0;
}

/* 提交按钮样式 */
.submit-btn {
  margin-top: 60rpx;
  border-radius: 12rpx;
  font-weight: 500;
  font-size: 32rpx;
  letter-spacing: 2rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 123, 255, 0.2);
  background: #007AFF !important;
  height: 90rpx;
  line-height: 90rpx;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 123, 255, 0.15);
}

/* 筛选区域样式 */
.filter-container {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.filter-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.category-scroll {
  white-space: nowrap;
  height: 80rpx;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.category-item.active {
  background-color: #1aad19;
  color: #fff;
}

/* 设备列表样式 */
.device-list {
  margin-top: 20rpx;
}

.device-item {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.device-info {
  display: flex;
  flex-direction: column;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.device-type {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.device-status {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
  width: fit-content;
}

.device-status.available {
  background-color: #e8f5e9;
  color: #4caf50;
}

.device-status.unavailable {
  background-color: #ffebee;
  color: #f44336;
}

.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading text {
  color: #999;
  font-size: 28rpx;
}

/* 日期时间选择器样式 */
.datetime-picker {
  background: rgba(245, 247, 250, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 16rpx;
}

.datetime-row {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.datetime-row:last-child {
  border-bottom: none;
}

.datetime-label {
  color: #666;
  font-size: 28rpx;
  margin-right: 24rpx;
}

.datetime-value {
  flex: 1;
  color: #1A1A1A;
  font-size: 28rpx;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  animation: fadeIn 0.3s ease-out;
}