需要设计一个预约系统小程序，要求如下：
前端使用微信小程序架构：首页要有场地预约、设备预约、3D打印机预约作为三大分栏，场地预约下面分讲座、研讨室、会议室三个按钮，设备预约下面有电动螺丝刀、万用表，3D打印机下面给个入口按钮就可以，总共三台3D打印机，场地预约点进去后的界面要求显示申请预约界面，该界面要包含你选择的场地类型，日期可选（选择日期以及复选框选择商务下午还是晚上，因为讲座需要的时间比较长）（场地需要提前三天预约），用途可选，设备有几个复选框分别是大屏 笔记本 话筒（手持麦和鹅颈麦）笔记本选中的话要绑定投屏器一起绑定，投屏器也可以单独选中。设备预约（设备可以随时预约）点进去后的界面要求显示申请设备预约的界面包含设备名称，借用时间为用户可选，预计归还时间用户可选，借用原因：文本框的形式输入。3D打印机的预约界面显示打印机名字（例如打印机1、2、3），打印时间（用户可选），打印机需要提前一天申请，申请就是一天，用户选择日期即可。上面是学生端或者老师端（预约端）的要求，除了首页，再添加一个我的界面，方面老师和学生登录系统，我的界面里面要加入预约记录的查看，因为设备的借用需要主动归还
第二个部分是审批界面（需要用管理员账号登入即可访问该界面），审批端要能看到预约端发送的全部申请记录，点开一条申请即可看到是什么类型的预约，预约的时间是什么，申请人是谁，是否同意审批。要能正确区分已经审批的和未审批的，同时也要进行学生设备归还的确认审核。审批端应该要能看到所有的借用记录和归还记录
第三个部分是管理界面，是可以单独做一个web而不是依赖于小程序，后端可以导入学生和老师的身份，用excel导入数据，后台要给数据模版，因为这里学生和老师共用预约端即可，只用区分预约端和管理员的账号即可，导入excel的格式是学号或者工号，学院，以及身份证号后6位作为密码，后端同时要将场地、设备、3D打印机和记录能用excel导出，包含设备名或者场地名，场地预约记录同时也要记录下设备的借用情况，借用时间，归还时间，借用人，原因之类的都要输出到excel表格里，同时后端管理界面也要能够显示设备的使用状态包含添加设备、添加场地、修改预约端的一些设备的数量之类的。
