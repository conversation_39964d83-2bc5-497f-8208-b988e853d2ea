#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
用于验证创新工坊预约系统的主要API接口
"""

import requests
import json
from datetime import datetime, date, timedelta

# 配置
BASE_URL = "http://localhost:8001"
TEST_USER = {
    "username": "testuser",
    "password": "testpassword"
}

def test_api():
    """测试主要API接口"""
    print("=" * 60)
    print("创新工坊预约系统API测试")
    print("=" * 60)
    
    session = requests.Session()
    token = None
    
    # 1. 测试根端点
    print("\n1. 测试根端点")
    try:
        response = session.get(f"{BASE_URL}/")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. 测试AI健康检查
    print("\n2. 测试AI健康检查")
    try:
        response = session.get(f"{BASE_URL}/api/ai/health")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   AI服务状态: {result.get('status')}")
            print(f"   配置状态: {result.get('configured')}")
            print(f"   运行模式: {result.get('mode')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试用户登录
    print("\n3. 测试用户登录")
    try:
        login_data = {
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        }
        response = session.post(
            f"{BASE_URL}/api/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            token = result["access_token"]
            user_info = result["user_info"]
            print(f"   登录成功: {user_info['name']} ({user_info['role']})")
            print(f"   部门: {user_info['department']}")
            
            # 设置认证头
            session.headers.update({
                "Authorization": f"Bearer {token}"
            })
        else:
            print(f"   登录失败: {response.text}")
            return
    except Exception as e:
        print(f"   异常: {e}")
        return
    
    # 4. 测试获取用户信息
    print("\n4. 测试获取用户信息")
    try:
        response = session.get(f"{BASE_URL}/api/users/me")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            user_info = response.json()
            print(f"   用户ID: {user_info['id']}")
            print(f"   用户名: {user_info['username']}")
            print(f"   姓名: {user_info['name']}")
            print(f"   角色: {user_info['role']}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 5. 测试获取设备列表
    print("\n5. 测试获取设备列表")
    try:
        response = session.get(f"{BASE_URL}/api/management/devices")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            devices = response.json()
            print(f"   设备总数: {len(devices)}")
            for i, device in enumerate(devices[:3]):  # 只显示前3个
                print(f"   设备{i+1}: {device.get('device_or_venue_name')} "
                      f"({device.get('category')}) - "
                      f"可用: {device.get('available_quantity')}/{device.get('quantity')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 6. 测试获取场地列表
    print("\n6. 测试获取场地列表")
    try:
        response = session.get(f"{BASE_URL}/api/management/venues")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            venues = response.json()
            print(f"   场地总数: {len(venues)}")
            for i, venue in enumerate(venues[:3]):  # 只显示前3个
                print(f"   场地{i+1}: {venue.get('device_or_venue_name')} - "
                      f"状态: {venue.get('status')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 7. 测试获取我的预约记录
    print("\n7. 测试获取我的预约记录")
    try:
        response = session.get(f"{BASE_URL}/api/reservations/my-reservations")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   总预约数: {result.get('total', 0)}")
            print(f"   当前页: {result.get('page', 1)}")
            print(f"   每页记录数: {result.get('page_size', 10)}")
            
            # 显示各类预约数量
            venue_count = len(result.get('venue_reservations', []))
            device_count = len(result.get('device_reservations', []))
            printer_count = len(result.get('printer_reservations', []))
            print(f"   场地预约: {venue_count}, 设备预约: {device_count}, 打印机预约: {printer_count}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 8. 测试获取场地占用时间
    print("\n8. 测试获取场地占用时间")
    try:
        today = date.today().strftime("%Y-%m-%d")
        response = session.get(
            f"{BASE_URL}/api/reservations/venue/occupied-times",
            params={"venue_type": "会议室", "date": today}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   查询日期: {result.get('date')}")
            print(f"   场地类型: {result.get('venue_type')}")
            occupied_times = result.get('occupied_times', [])
            if occupied_times:
                print(f"   已占用时间段: {', '.join(occupied_times)}")
            else:
                print(f"   该日期该场地无预约")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 9. 测试AI功能状态
    print("\n9. 测试AI功能状态")
    try:
        response = session.get(f"{BASE_URL}/api/settings/ai-feature")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   AI功能启用: {result.get('enabled')}")
            print(f"   功能描述: {result.get('description')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 10. 测试管理员功能（如果有权限）
    print("\n10. 测试管理员功能")
    try:
        response = session.get(f"{BASE_URL}/api/admin/reservations/pending")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   待审批预约总数: {result.get('total', 0)}")
            venue_pending = len(result.get('venue_reservations', []))
            device_pending = len(result.get('device_reservations', []))
            printer_pending = len(result.get('printer_reservations', []))
            print(f"   场地待审批: {venue_pending}, 设备待审批: {device_pending}, 打印机待审批: {printer_pending}")
        elif response.status_code == 403:
            print(f"   权限不足: 需要管理员权限")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 11. 测试AI聊天（如果有权限）
    print("\n11. 测试AI聊天")
    try:
        chat_data = {
            "message": "你好，我想了解预约系统的功能"
        }
        response = session.post(
            f"{BASE_URL}/api/ai/chat",
            json=chat_data
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('response', '')
            print(f"   AI回复: {ai_response[:100]}{'...' if len(ai_response) > 100 else ''}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print("\n" + "=" * 60)
    print("API测试完成")
    print("=" * 60)
    
    # 生成测试报告
    print("\n测试总结:")
    print("- 系统基础功能正常")
    print("- 用户认证机制工作正常")
    print("- 预约相关接口可用")
    print("- 管理功能需要相应权限")
    print("- AI功能状态可查询")
    print("\n建议:")
    print("- 可以通过 http://localhost:8001/docs 查看完整API文档")
    print("- 使用Postman或其他工具进行更详细的测试")
    print("- 测试完成后注意清理测试数据")

if __name__ == "__main__":
    test_api()
