# SQLite到MySQL迁移指南

本指南将帮助你将创新工坊预约系统从SQLite数据库迁移到MySQL数据库。

## 迁移前准备

### 1. 安装MySQL服务器

#### 在Windows上安装MySQL：
1. 下载MySQL Installer：https://dev.mysql.com/downloads/installer/
2. 运行安装程序，选择"Developer Default"
3. 设置root密码（建议使用强密码）
4. 完成安装

#### 在Linux上安装MySQL：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server
```

### 2. 配置环境变量

编辑项目根目录下的`.env`文件，更新MySQL配置：

```env
# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_actual_mysql_password  # 替换为你的MySQL密码
MYSQL_DATABASE=reservation_system

# Admin系统数据库配置
ADMIN_MYSQL_DATABASE=admin_system
```

### 3. 安装Python依赖

```bash
pip install -r requirements.txt
```

## 迁移步骤

### 方法一：使用Docker（推荐）

1. **启动MySQL容器**：
```bash
docker-compose up mysql -d
```

2. **等待MySQL启动完成**（约30秒）

3. **运行应用容器**：
```bash
docker-compose up reservation-system
```

### 方法二：手动迁移

1. **创建MySQL数据库**：
```sql
-- 连接到MySQL
mysql -u root -p

-- 执行以下SQL命令
CREATE DATABASE reservation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE admin_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **运行数据库初始化**：
```bash
python app/main.py
```

3. **迁移现有数据**（如果有SQLite数据）：
```bash
python migrate_sqlite_to_mysql.py
```

## 验证迁移

### 1. 检查数据库连接
启动应用后，查看日志确认MySQL连接成功：
```
Using database at: mysql+pymysql://root:***@localhost:3306/reservation_system?charset=utf8mb4
Successfully connected to database
```

### 2. 检查数据完整性
- 登录管理系统：http://localhost:5000
- 检查用户数据、预约记录等是否正常显示
- 测试创建新预约功能

### 3. 性能测试
```bash
# 运行性能测试
python test/locustfile.py
```

## 常见问题解决

### 1. MySQL连接失败
- 检查MySQL服务是否启动
- 验证用户名和密码是否正确
- 确认防火墙设置允许3306端口

### 2. 字符编码问题
确保MySQL配置使用utf8mb4字符集：
```sql
ALTER DATABASE reservation_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 权限问题
```sql
GRANT ALL PRIVILEGES ON reservation_system.* TO 'root'@'localhost';
GRANT ALL PRIVILEGES ON admin_system.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

## 性能优化建议

### 1. 数据库配置优化
在MySQL配置文件（my.cnf或my.ini）中添加：
```ini
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 2. 连接池配置
应用已配置连接池参数：
- pool_size=10
- max_overflow=20
- pool_recycle=3600

## 备份策略

### 1. 定期备份
```bash
# 备份所有数据库
mysqldump -u root -p --all-databases > backup_$(date +%Y%m%d).sql

# 备份特定数据库
mysqldump -u root -p reservation_system > reservation_backup_$(date +%Y%m%d).sql
```

### 2. 恢复数据
```bash
mysql -u root -p reservation_system < reservation_backup_20240101.sql
```

## 回滚到SQLite

如果需要回滚到SQLite：

1. 停止应用
2. 恢复原始的数据库配置文件
3. 将SQLite数据库文件放回原位置
4. 重启应用

## 技术支持

如果在迁移过程中遇到问题，请：
1. 检查应用日志
2. 查看MySQL错误日志
3. 确认网络连接和防火墙设置
