# 安全文档使用说明

## 📚 文档概览

本目录包含了创新工坊预约系统的完整安全文档，帮助开发和运维团队确保系统安全。

### 📄 文档列表

| 文档名称 | 用途 | 目标用户 |
|---------|------|----------|
| `网络安全隐患分析报告.md` | 详细的安全风险分析 | 开发团队、安全团队 |
| `安全修复实施指南.md` | 具体的修复步骤和代码 | 开发团队 |
| `安全部署检查清单.md` | 部署前后的安全检查 | 运维团队、DevOps |
| `安全文档使用说明.md` | 文档使用指南 | 所有团队成员 |

### 🛠️ 工具文件

| 文件名称 | 用途 | 使用方法 |
|---------|------|----------|
| `.env.template` | 环境变量配置模板 | 复制为.env并填入实际值 |
| `scripts/security_check.py` | 自动化安全检查脚本 | `python scripts/security_check.py` |
| `.gitignore` | 防止敏感文件泄露 | Git自动使用 |

## 🚀 快速开始

### 1. 立即修复高风险问题

**优先级：🚨 紧急**

```bash
# 1. 生成安全的JWT密钥
python -c "import secrets; print('JWT_SECRET_KEY=' + secrets.token_hex(32))"

# 2. 复制环境变量模板
cp .env.template .env

# 3. 编辑.env文件，填入实际值
nano .env

# 4. 运行安全检查
python scripts/security_check.py
```

### 2. 配置CORS安全策略

**修改文件：** `app/main.py`

```python
# 替换
allow_origins=["*"]

# 为
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")
allow_origins=ALLOWED_ORIGINS
```

### 3. 验证修复效果

```bash
# 运行完整安全检查
python scripts/security_check.py

# 检查依赖漏洞
pip install safety
safety check -r requirements.txt

# 测试CORS配置
curl -X OPTIONS http://localhost:8001/api/users/me \
  -H "Origin: http://malicious-site.com"
```

## 📋 使用流程

### 开发阶段

1. **代码开发前**
   - 阅读 `安全修复实施指南.md`
   - 了解安全编码规范
   - 配置开发环境安全设置

2. **代码开发中**
   - 遵循输入验证规范
   - 避免硬编码敏感信息
   - 使用参数化查询

3. **代码提交前**
   - 运行 `python scripts/security_check.py`
   - 修复发现的安全问题
   - 确保.env文件不被提交

### 测试阶段

1. **功能测试**
   - 验证认证授权功能
   - 测试输入验证机制
   - 检查错误处理安全性

2. **安全测试**
   - 运行自动化安全扫描
   - 进行手动渗透测试
   - 验证安全配置

### 部署阶段

1. **部署前检查**
   - 使用 `安全部署检查清单.md`
   - 验证生产环境配置
   - 确认所有安全措施就位

2. **部署过程**
   - 遵循安全部署流程
   - 监控部署过程
   - 记录部署日志

3. **部署后验证**
   - 运行功能验证测试
   - 检查安全配置生效
   - 验证监控系统正常

## 🔧 工具使用指南

### 安全检查脚本

```bash
# 基本用法
python scripts/security_check.py

# 查看帮助
python scripts/security_check.py --help

# 生成详细报告
python scripts/security_check.py --verbose

# 只检查特定类型
python scripts/security_check.py --check-type env,cors,sql
```

### 环境变量配置

```bash
# 1. 复制模板
cp .env.template .env

# 2. 生成安全密钥
echo "JWT_SECRET_KEY=$(python -c 'import secrets; print(secrets.token_hex(32))')" >> .env

# 3. 设置数据库密码
echo "MYSQL_PASSWORD=$(openssl rand -base64 32)" >> .env

# 4. 配置CORS域名
echo "ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com" >> .env
```

### 依赖安全检查

```bash
# 安装安全检查工具
pip install safety bandit

# 检查依赖漏洞
safety check -r requirements.txt

# 代码安全扫描
bandit -r app/ -f json -o security_report.json

# 查看扫描结果
cat security_report.json | jq '.results[] | select(.issue_severity=="HIGH")'
```

## 📊 安全监控

### 日志监控

```bash
# 查看安全日志
tail -f logs/security.log

# 搜索登录失败
grep "login_failed" logs/security.log

# 统计安全事件
grep -c "HIGH" logs/security.log
```

### 性能监控

```bash
# 检查API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8001/api/health

# 监控数据库连接
docker exec reservation-mysql mysqladmin processlist

# 检查容器资源使用
docker stats reservation-system
```

## 🚨 应急响应

### 发现安全问题时

1. **立即行动**
   ```bash
   # 停止服务
   docker-compose down
   
   # 备份日志
   cp -r logs/ backup/logs-$(date +%Y%m%d-%H%M%S)/
   
   # 分析问题
   python scripts/security_check.py
   ```

2. **评估影响**
   - 检查受影响的用户数量
   - 确定数据泄露范围
   - 评估业务影响

3. **修复问题**
   - 参考 `安全修复实施指南.md`
   - 实施紧急修复措施
   - 验证修复效果

4. **恢复服务**
   ```bash
   # 重新部署
   docker-compose up -d
   
   # 验证服务
   python scripts/security_check.py
   
   # 监控日志
   docker-compose logs -f
   ```

### 联系方式

- **技术负责人**: [技术团队邮箱]
- **安全负责人**: [安全团队邮箱]
- **应急热线**: [24小时联系电话]

## 📈 持续改进

### 定期任务

| 任务 | 频率 | 负责人 |
|------|------|--------|
| 安全检查脚本运行 | 每日 | 开发团队 |
| 依赖漏洞扫描 | 每周 | 开发团队 |
| 安全配置审查 | 每月 | 安全团队 |
| 渗透测试 | 每季度 | 外部团队 |

### 文档更新

- 发现新的安全问题时更新分析报告
- 修复方案验证后更新实施指南
- 部署流程变更时更新检查清单
- 定期审查和更新所有文档

### 培训计划

1. **新员工入职**
   - 安全意识培训
   - 文档使用培训
   - 实际操作演练

2. **定期培训**
   - 最新安全威胁介绍
   - 工具使用更新
   - 案例分析讨论

## 🔗 相关资源

### 外部资源

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [FastAPI Security](https://fastapi.tiangolo.com/tutorial/security/)
- [Docker Security](https://docs.docker.com/engine/security/)

### 内部资源

- 项目架构文档
- API接口文档
- 部署运维手册

## ❓ 常见问题

### Q: 如何快速修复高风险问题？

A: 按照以下顺序修复：
1. 配置JWT密钥环境变量
2. 限制CORS策略
3. 运行安全检查脚本验证

### Q: 部署前必须检查哪些项目？

A: 参考 `安全部署检查清单.md` 中的"部署前检查"部分，重点关注：
- 环境变量配置
- CORS策略
- 调试模式关闭
- 依赖安全扫描

### Q: 如何监控系统安全状态？

A: 建议：
- 每日运行安全检查脚本
- 监控安全日志异常
- 定期检查依赖漏洞
- 设置自动化监控告警

### Q: 发现安全问题如何处理？

A: 遵循应急响应流程：
1. 立即隔离问题
2. 评估影响范围
3. 实施修复措施
4. 验证修复效果
5. 更新安全策略

---

**文档维护**

- **创建时间**: 2025年6月14日
- **最后更新**: 2025年6月14日
- **维护团队**: 系统开发团队
- **审核周期**: 每月

*如有问题或建议，请联系开发团队。*
