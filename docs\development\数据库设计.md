# 🗄️ 数据库设计文档

## 概述

创新工坊预约系统采用MySQL数据库，支持高并发访问和数据一致性保证。系统分为主业务数据库和管理系统数据库两个独立的数据库实例。

## 数据库架构

### 主数据库 (reservation_system)
存储核心业务数据，包括用户信息、预约记录等。

### 管理数据库 (admin_system)
存储管理系统相关数据，包括管理员账户、系统配置等。

## 核心数据表

### 用户相关表

#### users (用户表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| user_id | INT | 用户ID | 主键，自增 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| email | VARCHAR(100) | 邮箱 | 唯一 |
| phone | VARCHAR(20) | 手机号 | |
| role | VARCHAR(20) | 用户角色 | 默认'student' |
| is_active | BOOLEAN | 是否激活 | 默认True |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

### 预约相关表

#### venue_reservations (场地预约表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| reservation_id | INT | 预约ID | 主键，自增 |
| user_id | INT | 用户ID | 外键 |
| venue_type | VARCHAR(100) | 场地类型 | 非空 |
| reservation_date | DATE | 预约日期 | 非空 |
| business_time | VARCHAR(50) | 使用时段 | 非空 |
| purpose | VARCHAR(500) | 使用目的 | |
| status | VARCHAR(50) | 预约状态 | 默认'pending' |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

#### device_reservations (设备预约表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| reservation_id | INT | 预约ID | 主键，自增 |
| user_id | INT | 用户ID | 外键 |
| device_name | VARCHAR(100) | 设备名称 | 非空 |
| borrow_time | DATETIME | 借用时间 | 非空 |
| return_time | DATETIME | 归还时间 | 非空 |
| usage_type | VARCHAR(50) | 使用类型 | 默认'takeaway' |
| teacher_name | VARCHAR(100) | 指导老师 | |
| status | VARCHAR(50) | 预约状态 | 默认'pending' |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

#### printer_reservations (3D打印预约表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| reservation_id | INT | 预约ID | 主键，自增 |
| user_id | INT | 用户ID | 外键 |
| start_time | DATETIME | 开始时间 | 非空 |
| estimated_duration | INT | 预计时长(分钟) | 非空 |
| model_name | VARCHAR(100) | 模型名称 | |
| teacher_name | VARCHAR(100) | 指导老师 | |
| status | VARCHAR(50) | 预约状态 | 默认'pending' |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

### 管理相关表

#### management (管理员表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 管理员ID | 主键，自增 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| password_hash | VARCHAR(255) | 密码哈希 | 非空 |
| role | VARCHAR(20) | 角色 | 默认'admin' |
| is_active | BOOLEAN | 是否激活 | 默认True |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

#### system_settings (系统设置表)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | INT | 设置ID | 主键，自增 |
| key | VARCHAR(100) | 配置键 | 唯一，非空 |
| value | VARCHAR(500) | 配置值 | |
| description | VARCHAR(255) | 描述 | |
| is_enabled | BOOLEAN | 是否启用 | 默认True |
| created_at | DATETIME | 创建时间 | 默认当前时间 |

## 索引设计

### 主要索引
- users表：username, email 唯一索引
- venue_reservations表：user_id, reservation_date 复合索引
- device_reservations表：user_id, device_name, borrow_time 复合索引
- printer_reservations表：user_id, start_time 复合索引

## 数据一致性

### 外键约束
- 所有预约表的user_id字段都关联users表
- 启用级联更新，限制级联删除

### 事务处理
- 预约创建使用事务确保数据一致性
- 状态更新操作使用乐观锁防止并发冲突

## 性能优化

### 连接池配置
```python
pool_size=10        # 连接池大小
max_overflow=20     # 最大溢出连接数
pool_recycle=3600   # 连接回收时间
pool_pre_ping=True  # 连接预检查
```

### 查询优化
- 使用适当的索引加速查询
- 避免N+1查询问题
- 使用分页查询处理大数据集

## 备份策略

### 自动备份
- 每日自动备份数据库
- 保留最近30天的备份文件
- 备份文件压缩存储

### 恢复测试
- 定期进行备份恢复测试
- 确保备份文件的完整性和可用性

## 迁移历史

详细的数据库迁移记录请参考：[MySQL迁移指南](deployment/MySQL迁移指南.md)

---

*更多技术细节请参考相关源码和配置文件*
