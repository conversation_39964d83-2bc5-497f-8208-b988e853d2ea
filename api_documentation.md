# 创新工坊预约系统API文档

## 系统概述

创新工坊预约系统提供完整的RESTful API接口，支持场地预约、设备借用、3D打印机预约等功能。系统包含两个主要服务：

- **FastAPI后端服务**：http://localhost:8001 - 提供主要的API接口
- **Flask管理系统**：http://localhost:5000 - 提供后台管理界面

## 认证机制

系统使用JWT（JSON Web Token）进行用户认证。需要认证的接口必须在请求头中包含有效的JWT令牌：

```
Authorization: Bearer <access_token>
```

## API接口分类

### 1. 基础接口

#### GET /
- **描述**：API根端点，返回欢迎信息
- **需要认证**：否
- **响应示例**：
```json
{
  "message": "Welcome to Reservation System API"
}
```

#### GET /docs
- **描述**：FastAPI自动生成的API文档（Swagger UI）
- **需要认证**：否
- **访问地址**：http://localhost:8001/docs

### 2. 认证接口

#### POST /api/token
- **描述**：用户登录接口
- **需要认证**：否
- **请求格式**：application/x-www-form-urlencoded
- **请求参数**：
  - `username`：用户名
  - `password`：密码
- **响应示例**：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user_info": {
    "username": "testuser",
    "name": "测试用户",
    "role": "admin",
    "department": "测试部门"
  }
}
```

#### GET /api/users/me
- **描述**：获取当前登录用户信息
- **需要认证**：是
- **响应示例**：
```json
{
  "id": 1,
  "username": "testuser",
  "name": "测试用户",
  "role": "admin",
  "department": "测试部门"
}
```

### 3. 预约接口

#### GET /api/reservations/my-reservations
- **描述**：获取当前用户的预约记录
- **需要认证**：是
- **查询参数**：
  - `page`：页码，默认为1
  - `page_size`：每页记录数，默认为10
  - `status`：可选，按状态筛选（pending/approved/rejected）
  - `reservation_type`：可选，按预约类型筛选（venue/device/printer）
- **响应示例**：
```json
{
  "total": 25,
  "page": 1,
  "page_size": 10,
  "total_pages": 3,
  "venue_reservations": [...],
  "device_reservations": [...],
  "printer_reservations": [...]
}
```

#### POST /api/reservations/venue
- **描述**：创建场地预约
- **需要认证**：是
- **请求体**：
```json
{
  "venue_type": "会议室",
  "reservation_date": "2024-01-15",
  "business_time": "morning",
  "purpose": "团队会议",
  "devices_needed": {
    "screen": true,
    "laptop": false,
    "mic_handheld": true,
    "mic_gooseneck": false,
    "projector": true
  }
}
```

#### POST /api/reservations/device
- **描述**：创建设备预约
- **需要认证**：是
- **请求体**：
```json
{
  "device_name": "笔记本电脑",
  "borrow_time": "2024-01-15 09:00",
  "return_time": "2024-01-15 17:00",
  "reason": "项目开发",
  "usage_type": "takeaway",
  "teacher_name": "张老师"
}
```

#### POST /api/reservations/printer
- **描述**：创建打印机预约
- **需要认证**：是
- **请求体**：
```json
{
  "printer_name": "3D打印机A",
  "reservation_date": "2024-01-15",
  "print_time": "2024-01-15 10:00",
  "end_time": "2024-01-15 14:00",
  "estimated_duration": 240,
  "model_name": "手机壳模型",
  "teacher_name": "李老师"
}
```

#### GET /api/reservations/venue/occupied-times
- **描述**：获取特定日期和场地类型已被预约的时间段
- **需要认证**：是
- **查询参数**：
  - `venue_type`：场地类型，如"会议室"
  - `date`：日期，格式为YYYY-MM-DD
- **响应示例**：
```json
{
  "venue_type": "会议室",
  "date": "2024-01-15",
  "occupied_times": ["morning", "afternoon"]
}
```

### 4. 管理接口

#### GET /api/management/devices
- **描述**：获取所有设备列表
- **需要认证**：是
- **响应示例**：
```json
[
  {
    "management_id": 1,
    "device_or_venue_name": "笔记本电脑",
    "category": "device",
    "quantity": 5,
    "available_quantity": 3,
    "status": "available"
  }
]
```

#### GET /api/management/venues
- **描述**：获取所有场地列表
- **需要认证**：是
- **响应示例**：
```json
[
  {
    "management_id": 2,
    "device_or_venue_name": "会议室A",
    "category": "venue",
    "quantity": 1,
    "available_quantity": 1,
    "status": "available"
  }
]
```

#### POST /api/management/devices
- **描述**：添加新设备或场地
- **需要认证**：是
- **请求体**：
```json
{
  "device_or_venue_name": "新设备名称",
  "category": "device"
}
```

### 5. 管理员接口

#### GET /api/admin/reservations/pending
- **描述**：获取待审批的预约记录
- **需要认证**：是（需要管理员权限）
- **查询参数**：
  - `page`：页码，默认为1
  - `page_size`：每页记录数，默认为10
  - `reservation_type`：可选，按预约类型筛选
- **响应示例**：
```json
{
  "total": 15,
  "page": 1,
  "page_size": 10,
  "total_pages": 2,
  "venue_reservations": [...],
  "device_reservations": [...],
  "printer_reservations": [...]
}
```

#### GET /api/admin/reservations/approved
- **描述**：获取已审批的预约记录
- **需要认证**：是（需要管理员权限）
- **查询参数**：同上

#### POST /api/admin/reservations/approve
- **描述**：审批预约
- **需要认证**：是（需要管理员权限）
- **请求体**：
```json
{
  "type": "venue",
  "id": 123,
  "status": "approved"
}
```

#### GET /api/admin/users
- **描述**：获取用户列表
- **需要认证**：是（需要管理员权限）
- **查询参数**：
  - `page`：页码
  - `page_size`：每页记录数
  - `search`：搜索关键词
  - `role`：角色筛选

#### POST /api/admin/users/import
- **描述**：批量导入用户
- **需要认证**：是（需要管理员权限）
- **请求格式**：multipart/form-data
- **文件格式**：Excel文件

#### GET /api/admin/export-reservations
- **描述**：导出预约记录
- **需要认证**：是（需要管理员权限）
- **查询参数**：
  - `start_date`：开始日期
  - `end_date`：结束日期
- **响应**：Excel文件下载

### 6. AI接口

#### GET /api/ai/health
- **描述**：AI服务健康检查接口
- **需要认证**：否
- **响应示例**：
```json
{
  "status": "ok",
  "service": "ai",
  "configured": true,
  "mode": "production"
}
```

#### POST /api/ai/chat
- **描述**：与AI助手进行对话
- **需要认证**：是
- **请求体**：
```json
{
  "message": "你好，我想预约一个会议室"
}
```
- **响应示例**：
```json
{
  "response": "您好！我可以帮您预约会议室。请问您想在哪一天预约？",
  "suggested_actions": ["选择日期", "查看可用会议室"]
}
```

### 7. 设置接口

#### GET /api/settings/ai-feature
- **描述**：获取AI功能的启用状态
- **需要认证**：否
- **响应示例**：
```json
{
  "enabled": true,
  "key": "ai_feature_enabled",
  "description": "是否启用AI功能界面"
}
```

#### POST /api/settings/ai-feature
- **描述**：更新AI功能的启用状态
- **需要认证**：否
- **请求体**：
```json
{
  "enabled": true
}
```

## 状态码说明

- `200`：请求成功
- `201`：创建成功
- `400`：请求参数错误
- `401`：未授权（需要登录）
- `403`：禁止访问（权限不足）
- `404`：资源不存在
- `422`：请求参数验证失败
- `500`：服务器内部错误

## 数据模型

### 用户模型
```json
{
  "user_id": 1,
  "username": "2021001",
  "name": "张三",
  "department": "计算机学院",
  "role": "student"
}
```

### 场地预约模型
```json
{
  "reservation_id": 1,
  "user_id": 1,
  "venue_type": "会议室",
  "reservation_date": "2024-01-15",
  "business_time": "morning",
  "purpose": "团队会议",
  "devices_needed": {...},
  "status": "pending",
  "created_at": "2024-01-15T08:00:00"
}
```

### 设备预约模型
```json
{
  "reservation_id": 1,
  "user_id": 1,
  "device_name": "笔记本电脑",
  "borrow_time": "2024-01-15T09:00:00",
  "return_time": "2024-01-15T17:00:00",
  "reason": "项目开发",
  "usage_type": "takeaway",
  "status": "pending",
  "created_at": "2024-01-15T08:00:00"
}
```

### 打印机预约模型
```json
{
  "reservation_id": 1,
  "user_id": 1,
  "printer_name": "3D打印机A",
  "reservation_date": "2024-01-15",
  "print_time": "2024-01-15T10:00:00",
  "end_time": "2024-01-15T14:00:00",
  "estimated_duration": 240,
  "model_name": "手机壳模型",
  "status": "pending",
  "created_at": "2024-01-15T08:00:00"
}
```

## 错误处理

所有API接口都遵循统一的错误响应格式：

```json
{
  "detail": "错误描述信息"
}
```

## 测试用户

系统已创建测试用户：
- **用户名**：testuser
- **密码**：testpassword
- **角色**：admin

## 注意事项

1. 所有日期时间格式遵循ISO 8601标准
2. 分页查询默认每页10条记录
3. 管理员接口需要admin角色权限
4. 文件上传接口支持Excel格式
5. AI功能可通过设置接口动态开启/关闭
