# 📚 创新工坊预约系统 - 文档中心

欢迎来到创新工坊预约系统的文档中心！这里包含了系统的完整文档，按照不同用途进行分类整理。

## 📋 文档整理说明

本文档中心已于2025年6月15日完成全面整理，将分散在项目各处的文档和图片资源统一归档，建立了清晰的分类结构。详细的整理记录请查看 [文档整理总结](文档整理总结.md)。

## 📖 文档导航

### 👥 用户文档 (`user/`)
面向系统使用者的文档
- [用户操作手册](user/用户操作手册.md) - 详细的用户使用指南
- [快速开始指南](user/快速开始指南.md) - 新用户快速上手

### 💻 开发文档 (`development/`)
面向开发者和运维人员的技术文档
- [技术架构文档](development/技术架构.md) - 系统整体架构设计
- [数据库设计文档](development/数据库设计.md) - 数据库结构和设计说明
- [API接口文档](development/API接口文档.md) - 接口规范和使用说明
- [API接口详细文档](development/API接口详细文档.md) - 详细的API使用指南

#### 部署指南 (`development/deployment/`)
- [Docker部署指南](development/deployment/Docker部署指南.md) - 容器化部署方案
- [Docker快速启动指南](development/deployment/Docker快速启动指南.md) - 5分钟快速部署
- [MySQL迁移指南](development/deployment/MySQL迁移指南.md) - 数据库迁移方案
- [环境配置指南](development/deployment/环境配置指南.md) - 开发环境配置
- [服务器部署指南](development/deployment/服务器部署指南.md) - 生产环境部署
- [MySQL迁移指南](development/deployment/MySQL迁移指南.md) - 数据库迁移说明
- [环境配置指南](development/deployment/环境配置指南.md) - 开发和生产环境配置

### 🔒 安全文档 (`security/`)
系统安全相关的文档和报告
- [安全总览](security/安全总览.md) - 系统安全架构概述
- [渗透测试报告](security/渗透测试报告.md) - 安全测试结果
- [安全修复指南](security/安全修复指南.md) - 安全问题修复方案
- [安全部署检查清单](security/安全部署检查清单.md) - 部署安全检查项

### 📋 需求文档 (`requirements/`)
项目需求和规格说明
- [项目需求文档](requirements/项目需求文档.md) - 完整的项目需求说明

### 📊 报告文档 (`reports/`)
答辩、分析和规划类文档
- [技术创新点文档](reports/技术创新点文档.md) - 技术创新和优化说明
- [数据库选型分析报告](reports/数据库选型分析报告.md) - SQLite vs MySQL 分析
- [数据库一致性设计答辩稿](reports/数据库一致性设计答辩稿.md) - 答辩材料
- [技术扩展规划](reports/技术扩展规划.md) - 未来技术发展规划
- [架构设计文档](reports/架构设计文档.md) - 详细的系统架构设计

### 📝 变更记录 (`changelog/`)
系统版本和变更历史
- [CHANGELOG](changelog/CHANGELOG.md) - 版本变更记录
- [迁移总结](changelog/MIGRATION_SUMMARY.md) - 重大迁移记录

### 🖼️ 图片资源 (`images/`)
文档中使用的图片和图表资源
- `architecture/` - 架构图和技术图表
- `security/` - 安全相关图表
- `user-guide/` - 用户指南截图

## 🔍 如何使用这些文档

### 对于新用户
1. 先阅读 [快速开始指南](user/快速开始指南.md)
2. 详细了解功能请参考 [用户操作手册](user/用户操作手册.md)

### 对于开发者
1. 了解系统架构：[技术架构文档](development/技术架构.md)
2. 数据库相关：[数据库设计文档](development/数据库设计.md)
3. 部署系统：[部署指南](development/deployment/)

### 对于运维人员
1. 部署配置：[部署指南](development/deployment/)
2. 安全检查：[安全文档](security/)
3. 问题排查：[技术架构文档](development/技术架构.md)

### 对于项目管理者
1. 技术创新：[技术创新点文档](reports/技术创新点文档.md)
2. 发展规划：[技术扩展规划](reports/技术扩展规划.md)
3. 变更历史：[变更记录](changelog/)

## 📞 文档维护

如果您发现文档有错误或需要更新，请：
1. 提交 Issue 说明问题
2. 或直接提交 Pull Request 修复

---

*最后更新时间：2025-06-14*
*文档版本：v2.0*
