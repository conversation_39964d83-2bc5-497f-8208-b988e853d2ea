# 🚀 Docker快速启动指南

这是创新工坊预约系统的Docker快速启动指南，帮助你在5分钟内完成部署。

## 📋 前置检查

在开始之前，请确保你的系统满足以下要求：

### ✅ 系统要求

- [ ] 操作系统：Windows 10/11、macOS、或 Linux
- [ ] 可用内存：至少 2GB
- [ ] 可用磁盘空间：至少 5GB
- [ ] 网络连接：可访问 Docker Hub 和 GitHub

### ✅ 软件要求

- [ ] Docker Desktop 已安装并运行
- [ ] Docker Compose 可用（通常随 Docker Desktop 一起安装）

**检查Docker是否正常工作：**

```bash
docker --version
docker-compose --version
```

### 💡 Windows用户特别说明

本项目为Windows用户提供了专门的批处理脚本：

- **`backup.bat`** - Windows版备份脚本，支持彩色输出和进度提示
- **`restore.bat`** - Windows版恢复脚本，支持交互式操作
- **自动环境检查** - 脚本会自动检查Docker服务状态
- **友好界面** - 提供中文提示和确认对话框

**Windows用户推荐使用：**

- 使用 **PowerShell** 或 **命令提示符** 运行Docker命令
- 使用 **Git Bash** 可以运行Linux风格的命令
- 推荐安装 **Windows Terminal** 获得更好的终端体验

## 🎯 一键部署

### 步骤1：获取代码

```bash
# 方法1：使用Git克隆（推荐）
git clone https://github.com/zzyss-marker/newinno.git
cd newinno

# 方法2：下载ZIP包
# 访问 https://github.com/zzyss-marker/newinno/archive/main.zip
# 下载并解压到本地目录
```

### 步骤2：配置环境（可选）

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件（可选）
# 如果需要AI功能，请配置DEEPSEEK_API_KEY
# 其他配置项在Docker环境下会自动设置
```

### 步骤3：一键启动

```bash
# 构建并启动所有服务
docker-compose up -d --build
```

**启动过程说明：**

- 🔄 下载MySQL 8.0镜像（首次运行）
- 🏗️ 构建应用服务镜像
- 🗄️ 初始化MySQL数据库
- 🚀 启动应用服务
- 👤 自动创建管理员账号

### 步骤4：验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看启动日志
docker-compose logs -f
```

**预期看到的状态：**

```
NAME                   STATUS              PORTS
reservation-mysql      Up                  3306/tcp, 33060/tcp
reservation-system     Up                  0.0.0.0:5000->5000/tcp, 0.0.0.0:8001->8001/tcp
```

## 🌐 访问系统

### 管理系统

- **地址**：http://localhost:5000
- **用户名**：`admin`
- **密码**：`admin123`

### API服务

- **地址**：http://localhost:8001
- **API文档**：http://localhost:8001/docs
- **健康检查**：http://localhost:8001/health

### 微信小程序

- 使用微信开发者工具打开 `fore/` 目录
- 配置后端API地址为：`http://localhost:8001`

## 🔧 常用操作

### 查看服务状态

```bash
# 查看容器状态
docker-compose ps

# 查看资源使用情况
docker stats

# 查看日志
docker-compose logs -f reservation-system
```

### 服务控制

```bash
# 停止服务
docker-compose stop

# 重启服务
docker-compose restart

# 停止并删除容器（保留数据）
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

### 数据管理

```bash
# 备份数据
./backup.sh      # Linux/macOS
backup.bat       # Windows

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p123456

# 进入应用容器
docker-compose exec reservation-system bash
```

## ❓ 常见问题

### Q1: 端口被占用怎么办？

**A1**: 修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "5001:5000"  # 将5000改为5001
  - "8002:8001"  # 将8001改为8002
```

### Q2: 服务启动失败怎么办？

**A2**: 查看详细日志：

```bash
docker-compose logs reservation-system
```

常见原因：

- 端口被占用
- 内存不足
- Docker权限问题

### Q3: 如何重置系统？

**A3**: 完全重置（⚠️ 会丢失所有数据）：

```bash
docker-compose down -v
docker-compose up -d --build
```

### Q4: AI功能不工作怎么办？

**A4**: 检查配置：

1. 确保 `.env` 文件中配置了正确的 `DEEPSEEK_API_KEY`
2. 检查网络连接是否正常
3. 在管理系统中确认AI功能已开启

### Q5: 数据库连接失败怎么办？

**A5**: 重启MySQL容器：

```bash
docker-compose restart mysql
# 等待30秒后重启应用
docker-compose restart reservation-system
```

## 🔄 更新系统

### 更新到最新版本

```bash
# 1. 备份数据（重要！）
./backup.sh    # Linux/macOS
backup.bat     # Windows

# 2. 拉取最新代码
git pull origin main

# 3. 重新构建并启动
docker-compose down
docker-compose up -d --build

# 4. 验证更新
docker-compose ps
```

## 📊 性能优化

### 基础优化

```yaml
# 在docker-compose.yml中添加资源限制
services:
  reservation-system:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

### 生产环境优化

- 使用外部MySQL数据库
- 配置Redis缓存
- 启用负载均衡
- 配置HTTPS

详细优化方案请参考 [README.docker.md](README.docker.md)

## 🆘 获取帮助

如果遇到问题，请按以下顺序寻求帮助：

1. **查看日志**：`docker-compose logs -f`
2. **检查文档**：[README.docker.md](README.docker.md)
3. **搜索Issues**：[GitHub Issues](https://github.com/zzyss-marker/newinno/issues)
4. **提交新Issue**：描述问题、环境信息和错误日志

## 📚 更多资源

- 📖 [完整Docker文档](README.docker.md)
- 📖 [主项目文档](README.md)
- 🐛 [问题反馈](https://github.com/zzyss-marker/newinno/issues)
- 📧 [联系邮箱](mailto:<EMAIL>)

---

<div align="center">
  <p>🎉 <strong>恭喜！你已成功部署创新工坊预约系统</strong></p>
  <p>🚀 开始探索系统功能吧！</p>
</div>

## 🔖 快速命令参考

```bash
# 启动系统
docker-compose up -d --build

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止系统
docker-compose down

# 重启系统
docker-compose restart

# 备份数据
./backup.sh

# 进入容器
docker-compose exec reservation-system bash
docker-compose exec mysql mysql -u root -p123456

# 清理系统
docker-compose down -v
docker system prune -a
```
