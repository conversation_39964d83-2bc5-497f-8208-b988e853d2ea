services:
  mysql:
    image: mysql:8.0
    container_name: reservation-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: reservation_system
      MYSQL_USER: reservation_user
      MYSQL_PASSWORD: 123456
    # ports:
      # - "3306:3306"  # 不暴露MySQL端口到外部，只供容器内部访问
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - reservation-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  reservation-system:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: reservation-system
    restart: always
    ports:
      - "8001:8001"
      - "5000:5000"
    volumes:
      - app-data:/app
      - ./.env:/app/.env
    environment:
      - TZ=Asia/Shanghai
      - PYTHONPATH=/app
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=123456
      - MYSQL_DATABASE=reservation_system
      - ADMIN_MYSQL_DATABASE=admin_system
    networks:
      - reservation-network
    depends_on:
      mysql:
        condition: service_healthy

networks:
  reservation-network:
    driver: bridge

volumes:
  app-data:
    driver: local
  mysql-data:
    driver: local
