{% extends "base.html" %}

{% block title %}预约记录{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">预约记录管理</h2>

    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">筛选条件</h5>
            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="card-body collapse show" id="filterCollapse">
            <form id="filterForm" class="row g-3">
                <!-- 基本筛选条件 -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="startDate">开始日期</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="endDate">结束日期</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="reservationType">预约类型</label>
                        <select class="form-control" id="reservationType">
                            <option value="">全部类型</option>
                            <option value="venue">场地预约</option>
                            <option value="device">设备预约</option>
                            <option value="printer">打印机预约</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="reservationStatus">状态</label>
                        <select class="form-control" id="reservationStatus">
                            <option value="">全部状态</option>
                            <option value="pending">待审批</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已拒绝</option>
                            <option value="returned">已归还</option>
                        </select>
                    </div>
                </div>

                <!-- 扩展搜索选项 -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="userSearch">申请人</label>
                        <input type="text" class="form-control" id="userSearch" placeholder="输入申请人姓名">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="departmentSearch">学院</label>
                        <input type="text" class="form-control" id="departmentSearch" placeholder="输入学院名称">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="deviceCondition">设备状态</label>
                        <select class="form-control" id="deviceCondition">
                            <option value="">全部状态</option>
                            <option value="normal">正常</option>
                            <option value="damaged">故障</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="keywordSearch">关键词搜索</label>
                        <input type="text" class="form-control" id="keywordSearch" placeholder="设备名称/场地/备注">
                    </div>
                </div>

                <div class="col-12 mt-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportReservations()">
                            <i class="fas fa-file-export"></i> 导出
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">预约列表</h5>
            <div class="d-flex align-items-center">
                <div id="reservation-count" class="badge bg-info text-white me-3">加载中...</div>
                <div id="pagination-info" class="text-muted small me-2">页码: <span id="current-page">1</span>/<span id="total-pages">1</span></div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light sticky-top bg-light">
                        <tr>
                            <th width="8%">预约类型</th>
                            <th width="8%">申请人</th>
                            <th width="8%">学院</th>
                            <th width="10%">申请时间</th>
                            <th width="10%">借用时间</th>
                            <th width="10%">归还时间</th>
                            <th width="8%">使用类型</th>
                            <th width="8%">指导老师</th>
                            <th width="20%">详细信息</th>
                            <th width="5%">状态</th>
                            <th width="5%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="reservationList">
                        <tr>
                            <td colspan="11" class="text-center p-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载预约记录...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="d-flex align-items-center">
                    <label for="page-size-select" class="me-2">每页显示:</label>
                    <select id="page-size-select" class="form-select form-select-sm" style="width: 80px;">
                        <option value="20">20</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <nav aria-label="预约记录分页">
                    <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" id="prev-page">上一页</a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" id="next-page">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量，用于存储分页状态和缓存
let currentPage = 1;
let pageSize = 50;
let totalPages = 1;
let totalRecords = 0;

// 数据缓存 - 优化版本
const dataCache = {
    // 缓存结构: { "key": { data: [...], timestamp: Date.now(), total: 0, totalPages: 0 } }
    cache: {},

    // 最大缓存条目数
    maxEntries: 20,

    // 缓存有效期（毫秒）
    cacheLifetime: 3 * 60 * 1000, // 3分钟

    // 缓存键列表（按时间顺序）
    keyList: [],

    // 生成缓存键
    generateKey: function(params) {
        // 使用JSON.stringify生成唯一键，确保所有参数都被考虑
        return JSON.stringify(params);
    },

    // 设置缓存
    set: function(params, data) {
        const key = this.generateKey(params);

        // 如果键已存在，先从列表中移除
        const existingIndex = this.keyList.indexOf(key);
        if (existingIndex !== -1) {
            this.keyList.splice(existingIndex, 1);
        }

        // 添加到列表末尾（最新）
        this.keyList.push(key);

        // 存储数据
        this.cache[key] = {
            data: data.data || data,
            timestamp: Date.now(),
            total: data.total || data.length,
            totalPages: data.total_pages || 1
        };

        // 如果缓存条目超过最大数量，删除最旧的条目
        if (this.keyList.length > this.maxEntries) {
            const oldestKey = this.keyList.shift();
            delete this.cache[oldestKey];
        }

        // 清理过期缓存
        this.cleanExpired();
    },

    // 获取缓存
    get: function(params) {
        const key = this.generateKey(params);
        const cached = this.cache[key];

        // 如果缓存存在且未过期
        if (cached && (Date.now() - cached.timestamp < this.cacheLifetime)) {
            // 更新访问时间（将键移到列表末尾）
            const index = this.keyList.indexOf(key);
            if (index !== -1) {
                this.keyList.splice(index, 1);
                this.keyList.push(key);
            }
            return cached;
        }

        // 如果缓存已过期，删除它
        if (cached) {
            const index = this.keyList.indexOf(key);
            if (index !== -1) {
                this.keyList.splice(index, 1);
            }
            delete this.cache[key];
        }

        return null;
    },

    // 清理过期缓存
    cleanExpired: function() {
        const now = Date.now();
        const expiredKeys = [];

        // 找出所有过期的键
        this.keyList.forEach(key => {
            if (this.cache[key] && (now - this.cache[key].timestamp > this.cacheLifetime)) {
                expiredKeys.push(key);
            }
        });

        // 删除过期的缓存
        expiredKeys.forEach(key => {
            const index = this.keyList.indexOf(key);
            if (index !== -1) {
                this.keyList.splice(index, 1);
            }
            delete this.cache[key];
        });
    },

    // 清除所有缓存
    clear: function() {
        this.cache = {};
        this.keyList = [];
    }
};

// 表单提交事件
document.getElementById('filterForm').onsubmit = async function(e) {
    e.preventDefault();
    // 重置分页状态
    currentPage = 1;
    // 清除缓存，确保获取最新数据
    dataCache.clear();
    await loadReservations();
};

// 页面大小选择事件
document.getElementById('page-size-select').addEventListener('change', function() {
    pageSize = parseInt(this.value);
    currentPage = 1; // 重置到第一页
    loadReservations();
});

// 重置按钮事件
document.getElementById('filterForm').querySelector('button[type="button"].btn-secondary').addEventListener('click', function() {
    // 清除缓存
    dataCache.clear();
});

// 上一页按钮事件
document.getElementById('prev-page').addEventListener('click', function(e) {
    e.preventDefault();
    if (currentPage > 1) {
        currentPage--;
        loadReservations();
    }
});

// 下一页按钮事件
document.getElementById('next-page').addEventListener('click', function(e) {
    e.preventDefault();
    if (currentPage < totalPages) {
        currentPage++;
        loadReservations();
    }
});

// 加载预约记录函数 - 优化版本，将所有筛选条件传递给后端
async function loadReservations(forceRefresh = false) {
    // 获取筛选条件
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const reservationType = document.getElementById('reservationType').value;
    const reservationStatus = document.getElementById('reservationStatus').value;
    const userSearch = document.getElementById('userSearch').value.trim();
    const departmentSearch = document.getElementById('departmentSearch').value.trim();
    const deviceCondition = document.getElementById('deviceCondition').value;
    const keywordSearch = document.getElementById('keywordSearch').value.trim();

    // 更新UI状态
    document.getElementById('reservation-count').textContent = '加载中...';
    updatePaginationInfo();

    // 显示加载中状态
    const tbody = document.getElementById('reservationList');
    tbody.innerHTML = `
        <tr>
            <td colspan="11" class="text-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载预约记录...</p>
            </td>
        </tr>
    `;

    try {
        // 构建请求参数对象（包含所有筛选条件）
        const requestParams = {
            page: currentPage,
            page_size: pageSize,
            start_date: startDate,
            end_date: endDate,
            reservation_type: reservationType,
            status: reservationStatus,
            user_search: userSearch,
            department_search: departmentSearch,
            keyword_search: keywordSearch,
            device_condition: deviceCondition
        };

        // 生成缓存键
        const cacheKey = JSON.stringify(requestParams);

        // 检查缓存中是否有数据（如果强制刷新则跳过缓存）
        const cachedData = forceRefresh ? null : dataCache.get(requestParams);
        let result;

        if (cachedData && !forceRefresh) {
            // 使用缓存数据
            console.log('使用缓存数据');
            result = {
                data: cachedData.data,
                total: cachedData.total,
                total_pages: cachedData.totalPages,
                page: currentPage,
                page_size: pageSize
            };
        } else {
            // 构建API请求参数
            const params = new URLSearchParams();

            // 添加所有筛选参数
            Object.entries(requestParams).forEach(([key, value]) => {
                if (value) {
                    params.append(key, value);
                }
            });

            // 发送API请求
            console.log(`发送API请求: ${params.toString()}`);
            const response = await fetch(`/api/reservations?${params.toString()}`);

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || '获取数据失败');
            }

            // 解析响应数据
            result = await response.json();

            // 缓存数据（如果不是强制刷新）
            if (!forceRefresh) {
                dataCache.set(requestParams, result);
            }
        }

        // 处理分页信息
        if (result.total !== undefined) {
            // 新API格式
            totalRecords = result.total;
            totalPages = result.total_pages || Math.ceil(totalRecords / pageSize);
            currentPage = result.page || currentPage;

            // 更新分页UI
            updatePaginationInfo();
            updatePaginationControls();

            // 获取实际数据
            const data = result.data || [];

            // 更新记录数量显示
            document.getElementById('reservation-count').textContent = `共 ${totalRecords} 条记录`;

            // 渲染数据 - 不需要再进行客户端筛选，因为所有筛选条件已经传递给后端
            renderReservations(data);

        } else {
            // 兼容旧API格式
            const data = Array.isArray(result) ? result : [];
            totalRecords = data.length;
            totalPages = 1;
            currentPage = 1;

            // 更新分页UI
            updatePaginationInfo();
            updatePaginationControls();

            // 更新记录数量显示
            document.getElementById('reservation-count').textContent = `共 ${data.length} 条记录`;

            // 渲染数据
            renderReservations(data);
        }

    } catch (error) {
        console.error('加载预约记录失败:', error);

        tbody.innerHTML = `
            <tr>
                <td colspan="11" class="text-center p-4">
                    <div class="alert alert-danger mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载失败: ${error.message || '未知错误'}
                    </div>
                </td>
            </tr>
        `;

        // 更新记录数量显示为错误状态
        document.getElementById('reservation-count').textContent = '加载失败';
    }
}

// 更新分页信息显示
function updatePaginationInfo() {
    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;
}

// 更新分页控件状态
function updatePaginationControls() {
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');

    // 更新上一页按钮状态
    if (currentPage <= 1) {
        prevPageBtn.parentElement.classList.add('disabled');
    } else {
        prevPageBtn.parentElement.classList.remove('disabled');
    }

    // 更新下一页按钮状态
    if (currentPage >= totalPages) {
        nextPageBtn.parentElement.classList.add('disabled');
    } else {
        nextPageBtn.parentElement.classList.remove('disabled');
    }
}

// 渲染预约记录列表 - 优化版本，减少DOM操作
function renderReservations(data) {
    const tbody = document.getElementById('reservationList');

    if (data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="11" class="text-center p-4">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        没有找到匹配的预约记录
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // 使用DocumentFragment减少DOM重绘次数
    const fragment = document.createDocumentFragment();

    // 清空表格
    tbody.innerHTML = '';

    // 批量处理数据，每次处理20条
    const batchSize = 20;
    let currentIndex = 0;

    function processBatch() {
        // 如果已处理完所有数据，则停止
        if (currentIndex >= data.length) {
            return;
        }

        // 计算当前批次的结束索引
        const endIndex = Math.min(currentIndex + batchSize, data.length);

        // 处理当前批次的数据
        for (let i = currentIndex; i < endIndex; i++) {
            const record = data[i];
            const row = document.createElement('tr');

            // 获取申请人信息
            const userName = record.user?.name || `用户(ID:${record.user_id})`;
            const userDept = record.user?.department || '未知学院';

            // 获取申请时间（记录创建时间）
            const reservationDate = formatDate(record.created_at);

            // 获取预约日期/借用时间
            let bookingDate = '';
            if (record.type === 'venue') {
                bookingDate = formatDate(record.reservation_date);
            } else if (record.type === 'device') {
                bookingDate = formatDateTime(record.borrow_time);
            } else if (record.type === 'printer') {
                bookingDate = formatDateTime(record.print_time);
            }

            // 获取归还时间
            let returnTime = '';
            if (record.type === 'device' && record.return_time) {
                returnTime = formatDateTime(record.return_time);
            } else if (record.type === 'printer' && record.end_time) {
                returnTime = formatDateTime(record.end_time);
            }

            // 获取使用类型
            let usageType = '';
            if (record.type === 'device') {
                usageType = `<span class="badge badge-${record.usage_type === 'onsite' ? 'info' : 'warning'}">
                    ${record.usage_type === 'onsite' ? '现场使用' : '带走使用'}
                </span>`;
            } else if (record.type === 'venue') {
                usageType = `<span class="badge badge-info">
                    ${getBusinessTime(record.business_time)}
                </span>`;
            } else if (record.type === 'printer') {
                usageType = `<span class="badge badge-info">
                    打印使用
                </span>`;
            }

            // 获取指导老师
            const teacherName = record.teacher_name || '未指定';

            // 设置行内容
            row.innerHTML = `
                <td><span class="badge badge-${getReservationTypeClass(record.type)}">${getReservationType(record.type)}</span></td>
                <td>${userName}</td>
                <td>${userDept}</td>
                <td>${reservationDate}</td>
                <td>${bookingDate}</td>
                <td>${returnTime}</td>
                <td>${usageType}</td>
                <td>${teacherName}</td>
                <td>${getDetailsHtml(record)}</td>
                <td><span class="badge badge-${getStatusBadgeClass(record.status)}">${getStatusText(record.status)}</span></td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-danger" onclick="deleteReservation('${record.type}', ${record.id || record.reservation_id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            // 添加行到文档片段
            fragment.appendChild(row);
        }

        // 更新当前索引
        currentIndex = endIndex;

        // 将当前批次添加到DOM
        tbody.appendChild(fragment);

        // 如果还有数据未处理，则安排下一批次
        if (currentIndex < data.length) {
            // 使用requestAnimationFrame确保浏览器有时间渲染当前批次
            window.requestAnimationFrame(processBatch);
        }
    }

    // 开始处理第一批数据
    processBatch();
}

function getDetailsHtml(record) {
    switch(record.type) {
        case 'venue':
            let devicesHtml = '';
            if (record.devices_needed) {
                const deviceList = [];
                if (record.devices_needed.screen) deviceList.push('<span class="badge badge-device">大屏</span>');
                if (record.devices_needed.laptop) deviceList.push('<span class="badge badge-device">笔记本</span>');
                if (record.devices_needed.mic_handheld) deviceList.push('<span class="badge badge-device">手持麦</span>');
                if (record.devices_needed.mic_gooseneck) deviceList.push('<span class="badge badge-device">鹅颈麦</span>');
                if (record.devices_needed.projector) deviceList.push('<span class="badge badge-device">投屏器</span>');
                devicesHtml = deviceList.length > 0 ?
                    `<div class="device-list">${deviceList.join('')}</div>` : '';
            }

            return `
                <div class="reservation-details">
                    <div>
                        <strong>场地类型:</strong> ${getVenueType(record.venue_type)}
                    </div>
                    <div class="text-wrap"><strong>用途:</strong> ${record.purpose || '未提供'}</div>
                    ${devicesHtml}
                </div>
            `;
        case 'printer':
            // 检查打印机状态值的有效性
            let printerCondition = 'normal';
            if (record.printer_condition === 'damaged') {
                printerCondition = 'damaged';
            }

            // 增强打印机状态信息显示 - 始终显示状态框
            let printerConditionHtml = '';
            if (record.status === 'completed') {
                const conditionClass = printerCondition === 'normal' ? 'success' : 'danger';
                const conditionText = printerCondition === 'normal' ? '正常' : '故障';
                printerConditionHtml = `
                    <div class="condition-box ${conditionClass}-box">
                        <strong>打印机状态:</strong>
                        <span class="badge badge-${conditionClass} condition-badge">${conditionText}</span>
                        <span class="condition-value">[${record.printer_condition}]</span>
                        ${record.completion_note ? `<div class="condition-note text-wrap"><strong>备注:</strong> ${record.completion_note}</div>` : ''}
                    </div>
                `;
            } else {
                // 未完成显示占位状态框
                printerConditionHtml = `
                    <div class="condition-box pending-box">
                        <strong>打印机状态:</strong>
                        <span class="badge badge-secondary condition-badge">未提交</span>
                    </div>
                `;
            }

            return `
                <div class="reservation-details">
                    <div><strong>打印机:</strong> ${record.printer_name}</div>
                    ${record.estimated_duration ? `<div><strong>预计耗时:</strong> ${record.estimated_duration}分钟</div>` : ''}
                    ${record.model_name ? `<div><strong>模型名称:</strong> ${record.model_name}</div>` : ''}
                    ${printerConditionHtml}
                </div>
            `;
        case 'device':
            // 检查设备状态值的有效性
            let deviceCondition = 'normal';
            if (record.device_condition === 'damaged') {
                deviceCondition = 'damaged';
            }

            // 增强设备状态信息显示 - 始终显示状态框
            let deviceConditionHtml = '';
            if (record.status === 'returned') {
                const conditionClass = deviceCondition === 'normal' ? 'success' : 'danger';
                const conditionText = deviceCondition === 'normal' ? '正常' : '故障';
                deviceConditionHtml = `
                    <div class="condition-box ${conditionClass}-box">
                        <strong>设备状态:</strong>
                        <span class="badge badge-${conditionClass} condition-badge">${conditionText}</span>
                        <span class="condition-value">[${record.device_condition}]</span>
                        ${record.return_note ? `<div class="condition-note text-wrap"><strong>备注:</strong> ${record.return_note}</div>` : ''}
                    </div>
                `;
            } else {
                // 未归还显示占位状态框
                deviceConditionHtml = `
                    <div class="condition-box pending-box">
                        <strong>设备状态:</strong>
                        <span class="badge badge-secondary condition-badge">未归还</span>
                    </div>
                `;
            }

            return `
                <div class="reservation-details">
                    <div>
                        <strong>设备名称:</strong> ${getDeviceName(record.device_name)}
                    </div>
                    <div><strong>借用原因:</strong> ${record.reason || '(无借用原因)'}</div>
                    ${deviceConditionHtml}
                </div>
            `;
        default:
            return '';
    }
}

function formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

function formatDateTime(timeStr) {
    if (!timeStr) return '';
    try {
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) return '';
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('时间格式化错误:', error);
        return '';
    }
}

function formatTime(timeStr) {
    if (!timeStr) return '';
    try {
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) return '';
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('时间格式化错误:', error);
        return '';
    }
}

function getReservationType(type) {
    const typeMap = {
        'venue': '场地预约',
        'device': '设备预约',
        'printer': '打印机预约'
    };
    return typeMap[type] || type;
}

function getReservationTypeClass(type) {
    const typeClassMap = {
        'venue': 'primary',
        'device': 'success',
        'printer': 'warning'
    };
    return typeClassMap[type] || 'secondary';
}

function getStatusBadgeClass(status) {
    const statusMap = {
        'pending': 'warning',   // 待审批 - 黄色
        'approved': 'success',  // 已通过 - 绿色
        'rejected': 'danger',   // 已拒绝 - 红色
        'returned': 'info'      // 已归还 - 蓝绿色
    };
    return statusMap[status] || 'secondary';
}

function getStatusText(status) {
    const statusMap = {
        'pending': '待审批',
        'approved': '已通过',
        'rejected': '已拒绝',
        'returned': '已归还'
    };
    return statusMap[status] || status;
}

function getVenueType(type) {
    const venueMap = {
        'lecture': '讲座',
        'lecture_hall': '讲座厅',
        'seminar_room': '研讨室',
        'meeting_room': '会议室',
        'innovation_space': '创新工坊'
    };
    return venueMap[type] || type;
}

function getBusinessTime(time) {
    const timeMap = {
        'morning': '上午',
        'afternoon': '下午',
        'evening': '晚上'
    };
    return timeMap[time] || time;
}

function getDeviceName(name) {
    const deviceMap = {
        'electric_screwdriver': '电动螺丝刀',
        'multimeter': '万用表'
    };
    return deviceMap[name] || name;
}

async function deleteReservation(type, id) {
    if (!type || !id) {
        alert('缺少参数，无法删除');
        return;
    }

    if (!confirm(`确定要删除这条${getReservationType(type)}记录吗？此操作不可恢复！`)) {
        return;
    }

    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 删除中...';

    try {
        const response = await fetch(`/api/reservations/${type}/${id}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '删除失败');
        }

        // 显示成功消息
        const row = button.closest('tr');

        // 显示成功提示
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            删除成功！
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(successAlert);

        // 3秒后自动移除提示
        setTimeout(() => {
            if (successAlert.parentNode) {
                successAlert.remove();
            }
        }, 3000);

        // 立即开始删除动画
        row.style.backgroundColor = '#d4edda';
        row.style.transition = 'all 0.5s ease';

        setTimeout(() => {
            row.style.opacity = '0';
            row.style.transform = 'scale(0.95)';
            setTimeout(() => {
                // 立即从DOM中移除该行
                row.remove();

                // 清除缓存，确保获取最新数据
                dataCache.clear();

                // 检查当前页是否还有数据，如果当前页没有数据了，回到上一页
                const currentRows = document.querySelectorAll('#reservationList tr').length;
                if (currentRows === 0 && currentPage > 1) {
                    currentPage--;
                }

                // 强制重新加载数据，绕过缓存
                const timestamp = Date.now();
                loadReservations(true).then(() => {
                    console.log(`数据已强制刷新 - ${timestamp}`);
                }).catch(error => {
                    console.error('刷新数据失败:', error);
                    // 如果加载失败，尝试刷新整个页面
                    location.reload();
                });
            }, 300);
        }, 200);
    } catch (error) {
        console.error('删除失败：', error);
        alert('删除失败：' + error.message);

        // 恢复按钮状态
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

async function exportReservations() {
    const button = event.target;
    setButtonLoading(button, true);

    try {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reservationType = document.getElementById('reservationType').value;

        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (reservationType) params.append('type', reservationType);

        const response = await fetch(`/api/export/reservations?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '导出失败');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `预约记录_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
    } catch (error) {
        console.error('导出失败：', error);
        alert('导出失败：' + error.message);
    } finally {
        setButtonLoading(button, false);
    }
}

function handleApiError(error) {
    console.error('API错误：', error);
    if (error.response) {
        alert(`操作失败：${error.response.data?.error || '未知错误'}`);
    } else if (error.request) {
        alert('无法连接到服务器，请检查网络连接');
    } else {
        alert('请求错误：' + error.message);
    }
}

function resetFilters() {
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('reservationType').value = '';
    document.getElementById('reservationStatus').value = '';
    document.getElementById('userSearch').value = '';
    document.getElementById('departmentSearch').value = '';
    document.getElementById('deviceCondition').value = '';
    document.getElementById('keywordSearch').value = '';
    loadReservations();
}

loadReservations();
</script>

<style>
.reservation-details {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0.5rem 0;
}

.text-wrap {
    white-space: normal;
    word-break: break-word;
}

/* 优化徽章样式 */
.badge {
    font-size: 90%;
    font-weight: normal;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
    display: inline-block;
    margin-right: 0.3rem;
    margin-bottom: 0.2rem;
}

/* 恢复预约类型颜色 */
.badge-primary {
    background-color: #0d6efd;
    color: white;
}

.badge-success {
    background-color: #198754;
    color: white;
}

.badge-warning {
    background-color: #fd7e14;
    color: white;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

/* 恢复状态颜色 */
.badge-danger {
    background-color: #dc3545;
    color: white;
}

.badge-info {
    background-color: #0dcaf0;
    color: #212529;
}

/* 为详细信息添加动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.reservation-details {
    animation: fadeIn 0.3s ease-in-out;
}

/* 表格悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 固定表头样式 */
.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
}

/* 表格按钮样式 */
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}

/* 优化时间和使用方式文本 */
.time-text, .usage-text {
    margin-left: 0.5rem;
    font-weight: 500;
    color: #495057;
}

/* 设备列表样式 */
.device-list {
    margin-top: 0.5rem;
}

/* 增加表格间距 */
.table td, .table th {
    padding: 0.9rem 0.65rem;
    vertical-align: middle;
}

/* 表格行样式 */
.table tbody tr {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* 表头样式 */
.thead-light th {
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* 设备标签样式 */
.badge-device {
    background-color: #6c757d;
    color: white;
    opacity: 0.85;
}

/* 表格行间距 */
.table > tbody > tr > td {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

/* 增强表格分隔线 */
.table tbody tr {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* 表头粗体 */
.table th {
    font-weight: 600;
}

/* 设备状态相关样式 */
.condition-note {
    margin-top: 0.25rem;
    margin-left: 1rem;
    padding: 0.4rem 0.6rem;
    background-color: rgba(0,0,0,.03);
    border-left: 3px solid #dc3545;
    font-size: 0.9rem;
    border-radius: 0 0.25rem 0.25rem 0;
}

.condition-box {
    margin-top: 0.75rem;
    padding: 0.65rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(0,0,0,.155);
    box-shadow: 0 1px 3px rgba(0,0,0,.05);
}

.success-box {
    background-color: rgba(25, 135, 84, 0.05);
    border-color: rgba(25, 135, 84, 0.2);
}

.danger-box {
    background-color: rgba(220, 53, 69, 0.05);
    border-color: rgba(220, 53, 69, 0.2);
}

.pending-box {
    background-color: rgba(108, 117, 125, 0.05);
    border-color: rgba(108, 117, 125, 0.2);
}

.condition-badge {
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.35em 0.65em;
}

.badge-success {
    font-weight: 500;
}

.badge-danger {
    font-weight: 500;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

.condition-value {
    font-size: 0.8rem;
    color: #555;
    margin-left: 0.5rem;
    opacity: 0.8;
    font-family: monospace;
}

/* 确保状态显示的一致性 */
.badge-danger, .badge-success, .badge-secondary, .badge-warning {
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.35em 0.65em;
}
</style>
{% endblock %}