from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime
from sqlalchemy.sql import func
from ..database import Base

class SystemSettings(Base):
    """系统设置模型，用于存储全局系统配置"""
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True, nullable=False)
    value = Column(String(500), nullable=True)
    description = Column(String(255), nullable=True)
    is_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<SystemSettings(key='{self.key}', value='{self.value}', is_enabled={self.is_enabled})>"
