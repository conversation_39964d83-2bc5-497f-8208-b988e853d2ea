#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试FastAPI接口并生成API文档
"""

import requests
import json
from datetime import datetime, date
import sys
import traceback

# API基础URL
BASE_URL = "http://localhost:8001"
ADMIN_URL = "http://localhost:5000"

# 测试用户信息
TEST_USER = {
    "username": "testuser",
    "password": "testpassword"
}

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_info = None
        self.api_docs = []

    def add_to_docs(self, endpoint, method, description, request_params=None, response_example=None, auth_required=False, notes=None):
        """添加API文档"""
        self.api_docs.append({
            "endpoint": endpoint,
            "method": method,
            "description": description,
            "request_params": request_params,
            "response_example": response_example,
            "auth_required": auth_required,
            "notes": notes
        })

    def test_basic_endpoints(self):
        """测试基础端点"""
        print("=== 测试基础端点 ===")

        # 测试根端点
        try:
            response = self.session.get(f"{BASE_URL}/")
            print(f"GET / - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result}")
                self.add_to_docs(
                    endpoint="/",
                    method="GET",
                    description="API根端点，返回欢迎信息",
                    response_example=result,
                    auth_required=False
                )
        except Exception as e:
            print(f"GET / - Error: {e}")
            traceback.print_exc()

        # 测试AI健康检查
        try:
            response = self.session.get(f"{BASE_URL}/api/ai/health")
            print(f"GET /api/ai/health - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result}")
                self.add_to_docs(
                    endpoint="/api/ai/health",
                    method="GET",
                    description="AI服务健康检查接口",
                    response_example=result,
                    auth_required=False,
                    notes="此端点不需要认证，用于检查AI服务是否正常运行"
                )
        except Exception as e:
            print(f"GET /api/ai/health - Error: {e}")
            traceback.print_exc()

    def test_auth_endpoints(self):
        """测试认证端点"""
        print("\n=== 测试认证端点 ===")

        # 测试登录
        try:
            login_data = {
                "username": TEST_USER["username"],
                "password": TEST_USER["password"]
            }
            response = self.session.post(
                f"{BASE_URL}/api/token",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            print(f"POST /api/token - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                self.token = result["access_token"]
                self.user_info = result["user_info"]
                print(f"Login successful: {self.user_info}")

                # 设置认证头
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/token",
                    method="POST",
                    description="用户登录接口",
                    request_params={
                        "username": "用户名",
                        "password": "密码"
                    },
                    response_example={
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "bearer",
                        "user_info": {
                            "username": "testuser",
                            "name": "测试用户",
                            "role": "admin",
                            "department": "测试部门"
                        }
                    },
                    auth_required=False,
                    notes="请求格式为application/x-www-form-urlencoded"
                )
            else:
                print(f"Login failed: {response.text}")
        except Exception as e:
            print(f"POST /api/token - Error: {e}")
            traceback.print_exc()

        # 测试获取当前用户信息
        if self.token:
            try:
                response = self.session.get(f"{BASE_URL}/api/users/me")
                print(f"GET /api/users/me - Status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"User info: {result}")

                    # 添加到文档
                    self.add_to_docs(
                        endpoint="/api/users/me",
                        method="GET",
                        description="获取当前登录用户信息",
                        response_example=result,
                        auth_required=True,
                        notes="需要在请求头中包含有效的JWT令牌"
                    )
                else:
                    print(f"Error: {response.text}")
            except Exception as e:
                print(f"GET /api/users/me - Error: {e}")
                traceback.print_exc()

    def test_management_endpoints(self):
        """测试管理端点"""
        print("\n=== 测试管理端点 ===")

        if not self.token:
            print("需要先登录才能测试管理端点")
            return

        # 获取设备列表
        try:
            response = self.session.get(f"{BASE_URL}/api/management/devices")
            print(f"GET /api/management/devices - Status: {response.status_code}")
            if response.status_code == 200:
                devices = response.json()
                print(f"Found {len(devices)} devices")
                for device in devices[:3]:  # 只显示前3个
                    print(f"  - {device.get('device_or_venue_name', 'N/A')} ({device.get('category', 'N/A')})")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/management/devices",
                    method="GET",
                    description="获取所有设备列表",
                    response_example=devices[:2] if devices else [],
                    auth_required=True
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/management/devices - Error: {e}")
            traceback.print_exc()

        # 获取场地列表
        try:
            response = self.session.get(f"{BASE_URL}/api/management/venues")
            print(f"GET /api/management/venues - Status: {response.status_code}")
            if response.status_code == 200:
                venues = response.json()
                print(f"Found {len(venues)} venues")
                for venue in venues[:3]:  # 只显示前3个
                    print(f"  - {venue.get('device_or_venue_name', 'N/A')}")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/management/venues",
                    method="GET",
                    description="获取所有场地列表",
                    response_example=venues[:2] if venues else [],
                    auth_required=True
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/management/venues - Error: {e}")
            traceback.print_exc()

    def test_reservation_endpoints(self):
        """测试预约端点"""
        print("\n=== 测试预约端点 ===")

        if not self.token:
            print("需要先登录才能测试预约端点")
            return

        # 获取我的预约记录
        try:
            response = self.session.get(f"{BASE_URL}/api/reservations/my-reservations")
            print(f"GET /api/reservations/my-reservations - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Total reservations: {result.get('total', 0)}")
                print(f"Current page: {result.get('page', 1)}")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/reservations/my-reservations",
                    method="GET",
                    description="获取当前用户的预约记录",
                    request_params={
                        "page": "页码，默认为1",
                        "page_size": "每页记录数，默认为10",
                        "status": "可选，按状态筛选",
                        "reservation_type": "可选，按预约类型筛选"
                    },
                    response_example=result,
                    auth_required=True
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/reservations/my-reservations - Error: {e}")
            traceback.print_exc()

        # 获取场地占用时间
        try:
            today = date.today().strftime("%Y-%m-%d")
            response = self.session.get(
                f"{BASE_URL}/api/reservations/venue/occupied-times",
                params={"venue_type": "会议室", "date": today}
            )
            print(f"GET /api/reservations/venue/occupied-times - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Occupied times for 会议室 on {today}: {result.get('occupied_times', [])}")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/reservations/venue/occupied-times",
                    method="GET",
                    description="获取特定日期和场地类型已被预约的时间段",
                    request_params={
                        "venue_type": "场地类型，如'会议室'",
                        "date": "日期，格式为YYYY-MM-DD"
                    },
                    response_example=result,
                    auth_required=True
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/reservations/venue/occupied-times - Error: {e}")
            traceback.print_exc()

    def test_ai_endpoints(self):
        """测试AI端点"""
        print("\n=== 测试AI端点 ===")

        if not self.token:
            print("需要先登录才能测试AI端点")
            return

        # 测试AI聊天
        try:
            chat_data = {
                "message": "你好，我想预约一个会议室"
            }
            response = self.session.post(
                f"{BASE_URL}/api/ai/chat",
                json=chat_data
            )
            print(f"POST /api/ai/chat - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"AI Response: {result.get('response', 'No response')[:100]}...")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/ai/chat",
                    method="POST",
                    description="与AI助手进行对话",
                    request_params={
                        "message": "用户发送的消息内容"
                    },
                    response_example={
                        "response": "您好！我可以帮您预约会议室。请问您想在哪一天预约？",
                        "suggested_actions": ["选择日期", "查看可用会议室"]
                    },
                    auth_required=True
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"POST /api/ai/chat - Error: {e}")
            traceback.print_exc()

    def test_admin_endpoints(self):
        """测试管理员端点"""
        print("\n=== 测试管理员端点 ===")

        if not self.token:
            print("需要先登录才能测试管理员端点")
            return

        # 获取待审批预约
        try:
            response = self.session.get(f"{BASE_URL}/api/admin/reservations/pending")
            print(f"GET /api/admin/reservations/pending - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Total pending reservations: {result.get('total', 0)}")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/admin/reservations/pending",
                    method="GET",
                    description="获取待审批的预约记录",
                    request_params={
                        "page": "页码，默认为1",
                        "page_size": "每页记录数，默认为10",
                        "reservation_type": "可选，按预约类型筛选"
                    },
                    response_example=result,
                    auth_required=True,
                    notes="需要管理员权限"
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/admin/reservations/pending - Error: {e}")
            traceback.print_exc()

    def test_settings_endpoints(self):
        """测试设置端点"""
        print("\n=== 测试设置端点 ===")

        # 获取AI功能状态
        try:
            response = self.session.get(f"{BASE_URL}/api/settings/ai-feature")
            print(f"GET /api/settings/ai-feature - Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"AI feature enabled: {result.get('enabled', False)}")

                # 添加到文档
                self.add_to_docs(
                    endpoint="/api/settings/ai-feature",
                    method="GET",
                    description="获取AI功能的启用状态",
                    response_example=result,
                    auth_required=False,
                    notes="此端点不需要用户登录"
                )
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"GET /api/settings/ai-feature - Error: {e}")
            traceback.print_exc()

    def generate_api_docs(self):
        """生成API文档"""
        print("\n=== 生成API文档 ===")

        # 按端点分类
        categories = {
            "基础接口": [],
            "认证接口": [],
            "预约接口": [],
            "管理接口": [],
            "AI接口": [],
            "设置接口": []
        }

        for api in self.api_docs:
            endpoint = api["endpoint"]
            if endpoint.startswith("/api/admin"):
                categories["管理接口"].append(api)
            elif endpoint.startswith("/api/reservations"):
                categories["预约接口"].append(api)
            elif endpoint.startswith("/api/ai"):
                categories["AI接口"].append(api)
            elif endpoint.startswith("/api/settings"):
                categories["设置接口"].append(api)
            elif endpoint.startswith("/api/token") or endpoint.startswith("/api/users"):
                categories["认证接口"].append(api)
            else:
                categories["基础接口"].append(api)

        # 生成Markdown文档
        doc = "# 创新工坊预约系统API文档\n\n"
        doc += "## 目录\n\n"

        for category in categories:
            if categories[category]:
                doc += f"- [{category}](#{category.lower().replace(' ', '-')})\n"

        doc += "\n## 接口说明\n\n"
        doc += "所有需要认证的接口都需要在请求头中包含有效的JWT令牌：\n\n"
        doc += "```\nAuthorization: Bearer <access_token>\n```\n\n"

        for category, apis in categories.items():
            if not apis:
                continue

            doc += f"## {category}\n\n"

            for api in apis:
                doc += f"### {api['method']} {api['endpoint']}\n\n"
                doc += f"{api['description']}\n\n"

                if api['auth_required']:
                    doc += "**需要认证**：是\n\n"
                else:
                    doc += "**需要认证**：否\n\n"

                if api['request_params']:
                    doc += "**请求参数**：\n\n"
                    if api['method'] == "GET":
                        doc += "查询参数：\n\n"
                    else:
                        doc += "请求体：\n\n"

                    doc += "| 参数名 | 说明 |\n"
                    doc += "| --- | --- |\n"
                    for param, desc in api['request_params'].items():
                        doc += f"| {param} | {desc} |\n"
                    doc += "\n"

                if api['response_example']:
                    doc += "**响应示例**：\n\n"
                    doc += "```json\n"
                    doc += json.dumps(api['response_example'], ensure_ascii=False, indent=2)
                    doc += "\n```\n\n"

                if api['notes']:
                    doc += f"**备注**：{api['notes']}\n\n"

                doc += "---\n\n"

        # 保存文档
        with open("api_docs.md", "w", encoding="utf-8") as f:
            f.write(doc)

        print(f"API文档已生成：api_docs.md")
        return doc

    def run_all_tests(self):
        """运行所有测试"""
        print("开始API接口测试...")
        print(f"FastAPI URL: {BASE_URL}")
        print(f"Admin System URL: {ADMIN_URL}")
        print(f"Test User: {TEST_USER['username']}")
        print("=" * 50)

        self.test_basic_endpoints()
        self.test_auth_endpoints()
        self.test_management_endpoints()
        self.test_reservation_endpoints()
        self.test_ai_endpoints()
        self.test_admin_endpoints()
        self.test_settings_endpoints()

        # 生成API文档
        self.generate_api_docs()

        print("\n" + "=" * 50)
        print("API测试完成")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
