/* pages/approval/approval.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F2F6 0%, #F7F8FA 100%);
  padding-bottom: 30rpx;
}

.tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1AAD19;
  font-weight: 500;
}

.tab-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.tab-text {
  position: relative;
  z-index: 1;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1AAD19;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

.tab-item:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.records-list {
  padding: 20rpx;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 120rpx 0;
}

.record-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 不同类型预约的左边框颜色 */
.record-item.venue {
  border-left: 8rpx solid #1890FF;
}

.record-item.device {
  border-left: 8rpx solid #722ED1;
}

.record-item.printer {
  border-left: 8rpx solid #13C2C2;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

/* 不同类型预约的标签样式 */
.type-tag.venue {
  color: #1890FF;
  background: rgba(24, 144, 255, 0.1);
}

.type-tag.device {
  color: #722ED1;
  background: rgba(114, 46, 209, 0.1);
}

.type-tag.printer {
  color: #13C2C2;
  background: rgba(19, 194, 194, 0.1);
}

.status-tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

.status-tag.approved {
  color: #52C41A;
  background: rgba(82, 196, 26, 0.1);
}

.status-tag.rejected {
  color: #FF4D4F;
  background: rgba(255, 77, 79, 0.1);
}

.status-tag.pending {
  color: #FAAD14;
  background: rgba(250, 173, 20, 0.1);
}

.status-tag.returned {
  color: #1890FF;
  background: rgba(24, 144, 255, 0.1);
}

.status-tag.completed {
  color: #13C2C2;
  background: rgba(19, 194, 194, 0.1);
}

.time {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
  padding-bottom: 24rpx;
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
}

.label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.record-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding-top: 10rpx;
}

.btn {
  font-size: 26rpx;
  margin: 0;
  border-radius: 8rpx;
  padding: 12rpx 32rpx;
  transition: all 0.3s ease;
}

.btn.approve {
  background: #52C41A;
  color: #fff;
}

.btn.approve:active {
  background: #389E0D;
  transform: scale(0.98);
}

.btn.reject {
  background: #FF4D4F;
  color: #fff;
}

.btn.reject:active {
  background: #CF1322;
  transform: scale(0.98);
}

.btn[disabled] {
  opacity: 0.5;
  background: #ccc;
}

.loading {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1AAD19;
  border-radius: 50%;
  margin: 0 auto 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-more-tip {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx 0;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.loading-more .loading-icon {
  width: 30rpx;
  height: 30rpx;
  margin: 0 10rpx 0 0;
}

.loading-more .loading-text {
  font-size: 24rpx;
  color: #999;
}

.no-more-data {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx 0;
}

.devices-list {
  display: inline;
  word-break: keep-all;
  white-space: normal;
}

.devices-list text:last-child::after {
  content: '';
}

.devices-list text:not(:last-child)::after {
  content: '、';
}

/* 预约类型标签栏样式 */
.type-tabs {
  display: flex;
  width: 100%;
  height: 44px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.type-tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  font-size: 14px;
  color: #666;
}

.type-tab-item.active {
  color: #1890ff;
  font-weight: 500;
}

.type-tab-item.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.type-tab-line {
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 3px;
  background-color: #1890ff;
  border-radius: 2px;
}