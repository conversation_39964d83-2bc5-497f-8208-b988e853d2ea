# 服务器部署指南

## 📋 部署概述

本指南详细说明如何在生产服务器上部署创新工坊预约系统，包括环境配置、SSL证书申请、Nginx反向代理配置等。

## 🛠️ 环境准备

### 系统要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少20GB可用空间
- **网络**: 公网IP地址，开放80、443端口

### 基础软件安装

```bash
# 更新包列表
sudo apt update

# 安装 Python 相关工具
sudo apt install python3 python3-pip python3-venv

# 验证安装
python3 --version
pip3 --version

# 安装开发工具包
sudo apt install python3-dev build-essential

# 安装其他有用的工具
sudo apt install wget curl git

# 升级 pip
python3 -m pip install --upgrade pip
```

### Git配置

```bash
# 安装 Git
sudo apt install git

# 验证安装
git --version

# 配置全局用户名
git config --global user.name "Your Name"

# 配置全局邮箱
git config --global user.email "<EMAIL>"

# 查看配置
git config --list

# 生成 SSH 密钥
ssh-keygen -t ed25519 -C "<EMAIL>"

# 启动 ssh-agent
eval "$(ssh-agent -s)"

# 添加私钥到 ssh-agent
ssh-add ~/.ssh/id_ed25519

# 查看公钥（需要添加到 GitHub/GitLab）
cat ~/.ssh/id_ed25519.pub

# 测试 GitHub 连接
ssh -T **************
```

## 🌐 域名配置

### 1. 域名购买与备案

- 在阿里云/腾讯云购买域名（比如：yourdomain.com）
- 进行域名备案（预计 1-2 周）
- 添加域名解析：
  ```
  记录类型: A
  主机记录: api    # 这样你的API域名就是 api.yourdomain.com
  记录值: 你的服务器IP地址
  TTL: 600s
  ```

## 🔐 SSL证书申请

### 方法一：使用 Certbot（推荐）

```bash
# 首先卸载现有的 certbot 相关包
sudo apt remove certbot python3-certbot-nginx

# 安装 snapd（如果还没有安装）
sudo apt install snapd

# 安装 core snap
sudo snap install core

# 刷新 core
sudo snap refresh core

# 删除可能存在的旧版本 certbot
sudo apt remove certbot

# 通过 snap 安装 certbot
sudo snap install --classic certbot

# 创建符号链接
sudo ln -s /snap/bin/certbot /usr/bin/certbot

# 安装 nginx 插件
sudo snap install certbot-nginx

# 确保 nginx 正在运行
sudo systemctl status nginx

# 申请证书
sudo certbot --nginx -d api.yourdomain.com

# 如果是中文域名先运行
python3 -c "print('yourdomain.fun'.encode('idna').decode())"
```

## 🌐 Nginx配置

### 安装Nginx

```bash
# 安装 Nginx
sudo apt install nginx

# 创建 Nginx 配置文件
sudo nano /etc/nginx/sites-available/reservation-system
```

### Nginx配置文件

```nginx
# HTTP 服务器配置（将 HTTP 重定向到 HTTPS）
server {
    listen 80;                # 监听 80 端口（HTTP）
    listen [::]:80;          # 监听 IPv6 的 80 端口
    server_name yourdomain.com;  # 您的域名
  
    # 将所有 HTTP 请求重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS 服务器配置
server {
    # SSL 配置
    listen 443 ssl;          # 监听 443 端口（HTTPS）
    listen [::]:443 ssl;     # 监听 IPv6 的 443 端口
    server_name yourdomain.com;  # 您的域名

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;

    # 反向代理配置
    location / {
        proxy_pass http://localhost:8001;  # 转发到本地的 FastAPI 服务
    
        # 代理的 HTTP 设置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
    
        # 传递客户端信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 启用配置

```bash
# 创建符号链接到 sites-enabled 目录
sudo ln -s /etc/nginx/sites-available/reservation-system /etc/nginx/sites-enabled/

# 可选：删除默认配置的符号链接（如果不需要默认站点）
sudo rm /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 如果测试通过，重启 Nginx
sudo systemctl restart nginx
```

## 🚀 应用部署

### 1. 获取代码

```bash
# 克隆项目
git clone https://github.com/zzyss-marker/newinno.git
cd newinno

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 配置系统服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/reservation-system.service
```

服务文件内容：

```ini
[Unit]
Description=Reservation System FastAPI application
After=network.target

[Service]
User=ubuntu  # 替换为您的用户名
Group=ubuntu  # 替换为您的用户组
WorkingDirectory=/home/<USER>/newinno  # 替换为实际路径
Environment="PATH=/home/<USER>/newinno/venv/bin"
ExecStart=/home/<USER>/newinno/venv/bin/python runme.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start reservation-system

# 检查服务状态
sudo systemctl status reservation-system

# 设置开机自启
sudo systemctl enable reservation-system
```

## 📱 微信小程序配置

### 1. 修改小程序配置

```javascript
// fore/utils/request.js
const baseURL = 'https://api.yourdomain.com/api'  // 使用 HTTPS
```

### 2. 微信公众平台配置

- 登录 [微信公众平台](https://mp.weixin.qq.com/)
- 进入"开发管理" -> "开发设置"
- 在"服务器域名"下添加：
  ```
  request合法域名: https://api.yourdomain.com
  ```

### 3. 上传小程序代码

- 在微信开发者工具中取消勾选"不校验合法域名"
- 点击"上传"
- 在微信公众平台提交审核

## 🔒 安全加固

### 防火墙配置

```bash
# 配置防火墙
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 22  # SSH端口
sudo ufw enable

# 禁用外部直接访问应用端口
sudo ufw deny 8001
sudo ufw deny 5000
```

### 文件权限设置

```bash
# 设置正确的文件权限
sudo chown -R ubuntu:ubuntu /home/<USER>/newinno
sudo chmod -R 755 /home/<USER>/newinno
```

## 📊 监控与维护

### 日志配置

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/reservation-system
```

```
/var/log/reservation-system/*.log {
    daily
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 ubuntu ubuntu
}
```

### 监控检查

```bash
# 检查服务状态
sudo systemctl status nginx
sudo systemctl status reservation-system

# 检查日志
sudo tail -f /var/log/nginx/error.log
sudo journalctl -u reservation-system -f

# 检查端口
sudo netstat -tulpn | grep -E '80|443|8001'
```

## 🔄 备份策略

### 数据库备份脚本

```bash
#!/bin/bash
# backup.sh
backup_dir="/home/<USER>/backups"
date=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$backup_dir"

# 备份数据库文件
cp /home/<USER>/newinno/app.db "$backup_dir/app_$date.db"
cp /home/<USER>/newinno/admin_system.db "$backup_dir/admin_$date.db"

# 删除7天前的备份
find "$backup_dir" -name "*.db" -mtime +7 -delete

echo "Backup completed: $date"
```

```bash
# 设置定时备份
crontab -e

# 添加以下行（每天凌晨2点备份）
0 2 * * * /home/<USER>/backup.sh
```

## ✅ 部署检查清单

- [ ] 服务器环境配置完成
- [ ] 域名解析配置正确
- [ ] SSL证书申请成功
- [ ] Nginx配置正确
- [ ] 应用服务正常运行
- [ ] 防火墙配置完成
- [ ] 微信小程序域名配置
- [ ] 监控和日志配置
- [ ] 备份策略实施
- [ ] 性能测试通过

## 🆘 故障排除

### 常见问题

1. **SSL证书申请失败**
   - 检查域名解析是否正确
   - 确保80端口未被占用
   - 检查防火墙设置

2. **Nginx配置错误**
   - 使用 `sudo nginx -t` 测试配置
   - 检查配置文件语法
   - 查看错误日志

3. **应用服务启动失败**
   - 检查环境变量配置
   - 查看服务日志
   - 确认依赖安装完整

4. **微信小程序无法访问**
   - 确认域名已添加到合法域名列表
   - 检查HTTPS证书有效性
   - 验证API接口可访问性

---

**注意**: 部署过程中如遇到问题，请查看相应的日志文件进行排查，或联系技术支持团队。
